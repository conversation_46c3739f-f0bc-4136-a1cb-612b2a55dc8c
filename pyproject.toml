[project]
name = "simple-sketch-manager"
version = "1.0.0"
description = "简笔画素材管理软件 - 专为设计师、剪辑师、创作者打造的素材管理工具"
readme = "README.md"
requires-python = ">=3.11.8"
authors = [{ name = "简笔画工作室", email = "<EMAIL>" }]
license = { text = "MIT" }
keywords = ["素材管理", "简笔画", "PyQt6", "文件管理", "创作工具"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Operating System :: MacOS",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Office/Business",
]

[project.dependencies]
PyQt6 = ">=6.8.1"
Pillow = ">=10.4.0"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "pyinstaller>=5.0.0",
]

[project.scripts]
simple-sketch-manager = "main:main"

[build-system]
requires = ["setuptools>=67", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.json", "*.db", "*.png", "*.ico"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''