#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽到分类功能
"""

import sys
import os
sys.path.append('.')

def test_drag_to_category():
    """测试拖拽到分类的逻辑"""
    print("测试拖拽到分类逻辑...")
    
    # 模拟文件信息
    file_info = {
        'id': 123,  # 普通文件
        'name': 'test_file.png',
        'path': '/path/to/test_file.png',
        'category': '人物',
        'is_folder': False
    }
    
    folder_info = {
        'id': 0,  # 文件夹
        'name': 'test_folder',
        'path': '/path/to/test_folder',
        'category': '人物',
        'is_folder': True
    }
    
    target_category = '场景'
    
    print(f"测试文件拖拽: {file_info['name']} -> {target_category}")
    print(f"文件ID: {file_info['id']}")
    print(f"是否为文件夹: {file_info.get('is_folder', False)}")
    
    print(f"\n测试文件夹拖拽: {folder_info['name']} -> {target_category}")
    print(f"文件夹ID: {folder_info['id']}")
    print(f"是否为文件夹: {folder_info.get('is_folder', False)}")
    
    # 测试处理逻辑
    def test_file_processing(info, target):
        file_id = info.get('id')
        is_folder = info.get('is_folder', False)
        
        if is_folder and file_id == 0:
            print(f"✅ {info['name']} 会使用文件系统操作（文件夹）")
            return True
        elif file_id and file_id != 0:
            print(f"✅ {info['name']} 会使用数据库操作（普通文件）")
            return True
        else:
            print(f"❌ {info['name']} 无法处理")
            return False
    
    print("\n处理结果:")
    test_file_processing(file_info, target_category)
    test_file_processing(folder_info, target_category)
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_drag_to_category()
