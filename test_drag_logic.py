#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽逻辑
"""

import sys
import os
sys.path.append('.')

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

def test_drag_logic():
    """测试拖拽逻辑"""
    print("测试拖拽逻辑...")
    
    # 模拟键盘修饰符
    print("1. 测试默认拖拽（应该是移动）")
    modifiers = Qt.KeyboardModifier.NoModifier
    if modifiers & Qt.KeyboardModifier.ControlModifier:
        action = "复制"
    else:
        action = "移动"
    print(f"   结果: {action}")
    
    print("2. 测试Ctrl+拖拽（应该是复制）")
    modifiers = Qt.KeyboardModifier.ControlModifier
    if modifiers & Qt.KeyboardModifier.ControlModifier:
        action = "复制"
    else:
        action = "移动"
    print(f"   结果: {action}")
    
    print("测试完成！")

if __name__ == "__main__":
    test_drag_logic()
