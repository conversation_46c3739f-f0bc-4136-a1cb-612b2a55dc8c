#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简笔画素材管理软件 - 主程序入口
作者：AI Assistant
版本：1.0.0
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QTranslator, QLibraryInfo
from src.ui.main_window import MainWindow
from src.utils.config_manager import ConfigManager
from src.models.filesystem_manager import FileSystemManager
from src.utils.filesystem_watcher import start_watching


def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)
    app.setApplicationName("简笔画素材管理")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("简笔画工作室")

    # 安装翻译
    translator = QTranslator()
    # 获取Qt的翻译文件路径
    translations_path = QLibraryInfo.path(QLibraryInfo.LibraryPath.TranslationsPath)
    if translator.load("qt_zh_CN", translations_path):
        app.installTranslator(translator)
    else:
        # 尝试备用路径
        if translator.load("qt_zh_CN"):
            app.installTranslator(translator)
        else:
            print("警告: 无法加载中文翻译文件。")
    
    # 高DPI支持在PyQt6中是默认启用的，不再需要手动设置
    
    # 初始化配置管理器
    config_manager = ConfigManager()

    # 初始化文件系统管理器并清理
    storage_path = config_manager.get_storage_path()
    fs_manager = FileSystemManager(str(storage_path))
    deleted_count = fs_manager.cleanup_non_existent_files()
    if deleted_count > 0:
        print(f"文件系统清理: 移除了 {deleted_count} 个不存在的文件记录。")

    # 启动文件系统监视器
    observer = start_watching(fs_manager, storage_path)

    # 创建主窗口
    main_window = MainWindow(config_manager, fs_manager)
    main_window.show()
    
    # 运行应用程序
    exit_code = app.exec()
    observer.stop()
    observer.join()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
