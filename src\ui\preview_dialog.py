# -*- coding: utf-8 -*-
"""
预览对话框 - 双击文件时显示的详情预览窗口
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QScrollArea, QSlider, QWidget, QFrame)
from PyQt6.QtCore import Qt, QTimer, QProcess
from PyQt6.QtGui import QPixmap, QFont, QKeySequence, QShortcut
from pathlib import Path
from typing import Dict, Any
import os
import subprocess
import json

from utils.thumbnail import ThumbnailGenerator
from utils.config_manager import ConfigManager


class PreviewDialog(QDialog):
    """预览对话框"""
    
    def __init__(self, file_info: Dict[str, Any], config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.file_info = file_info
        self.config_manager = config_manager
        self.thumbnail_generator = ThumbnailGenerator(config_manager)

        # 音频播放相关
        self.audio_process = None
        self.audio_duration = 0
        self.audio_position = 0
        self.is_playing = False
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_position)

        self.init_ui()
        self.setup_shortcuts()
        self.load_preview()
        self.apply_styles()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"预览 - {self.file_info['name']}")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 文件名标签
        self.title_label = QLabel(self.file_info['name'])
        self.title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        toolbar_layout.addWidget(self.title_label)
        
        toolbar_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(close_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 主内容区域 - 只显示预览，不显示文件信息
        # 预览滚动区域
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_scroll.setMinimumSize(600, 400)

        # 预览标签
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setStyleSheet("background-color: #2b2b2b; border: 1px solid #5a5a5a;")
        self.preview_label.setMinimumSize(580, 380)

        self.preview_scroll.setWidget(self.preview_label)
        layout.addWidget(self.preview_scroll)
        
        # 底部提示
        tip_label = QLabel("提示: 按 Esc 键、点击预览区域或点击关闭按钮退出预览")
        tip_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tip_label.setStyleSheet("color: #888888; font-size: 12px;")
        layout.addWidget(tip_label)
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # Esc键关闭
        esc_shortcut = QShortcut(QKeySequence("Escape"), self)
        esc_shortcut.activated.connect(self.close)
        
        # 空格键关闭
        space_shortcut = QShortcut(QKeySequence("Space"), self)
        space_shortcut.activated.connect(self.close)
        
        # 回车键关闭
        enter_shortcut = QShortcut(QKeySequence("Return"), self)
        enter_shortcut.activated.connect(self.close)
    
    def load_preview(self):
        """加载预览内容"""
        file_path = self.file_info.get('path', '')
        if not file_path or not os.path.exists(file_path):
            self.preview_label.setText("文件不存在")
            return
        
        file_ext = Path(file_path).suffix.lower()
        
        # 图片文件
        if file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp', '.psd']:
            self.load_image_preview(file_path)
        # 视频文件
        elif file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']:
            self.load_video_preview(file_path)
        # 音频文件
        elif file_ext in ['.mp3', '.wav', '.flac', '.aac', '.ogg']:
            self.load_audio_preview(file_path)
        # 文本文件
        elif file_ext in ['.txt', '.md', '.json', '.xml', '.csv']:
            self.load_text_preview(file_path)
        else:
            self.preview_label.setText(f"不支持预览此类型文件\n({file_ext})")
    
    def load_image_preview(self, file_path: str):
        """加载图片预览"""
        try:
            pixmap = QPixmap(file_path)
            if pixmap.isNull():
                self.preview_label.setText("无法加载图片")
                return
            
            # 缩放图片以适应预览区域
            preview_size = self.preview_scroll.size()
            scaled_pixmap = pixmap.scaled(
                preview_size.width() - 20,
                preview_size.height() - 20,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            
            self.preview_label.setPixmap(scaled_pixmap)
            self.preview_label.resize(scaled_pixmap.size())
            
        except Exception as e:
            self.preview_label.setText(f"加载图片失败: {str(e)}")
    
    def load_video_preview(self, file_path: str):
        """加载视频预览"""
        # 显示视频图标和信息
        self.preview_label.setText(f"🎬 视频文件\n\n{Path(file_path).name}\n\n双击在默认播放器中打开")
        self.preview_label.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                border: 1px solid #5a5a5a;
                color: #ffffff;
                font-size: 16px;
                padding: 20px;
            }
        """)
    
    def load_audio_preview(self, file_path: str):
        """加载音频预览"""
        # 创建音频播放器界面
        audio_widget = QWidget()
        audio_layout = QVBoxLayout(audio_widget)

        # 音频信息
        info_label = QLabel(f"🎵 音频文件\n{Path(file_path).name}")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                color: #ffffff;
                font-size: 16px;
                padding: 20px;
                border: 1px solid #5a5a5a;
                border-radius: 5px;
            }
        """)
        audio_layout.addWidget(info_label)

        # 播放控制区域
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                background-color: #3c3c3c;
                border: 1px solid #5a5a5a;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        controls_layout = QVBoxLayout(controls_frame)

        # 进度条
        self.audio_slider = QSlider(Qt.Orientation.Horizontal)
        self.audio_slider.setMinimum(0)
        self.audio_slider.setMaximum(100)
        self.audio_slider.setValue(0)
        self.audio_slider.sliderPressed.connect(self.on_slider_pressed)
        self.audio_slider.sliderReleased.connect(self.on_slider_released)
        controls_layout.addWidget(self.audio_slider)

        # 时间标签
        time_layout = QHBoxLayout()
        self.current_time_label = QLabel("00:00")
        self.current_time_label.setStyleSheet("color: #ffffff;")
        self.total_time_label = QLabel("00:00")
        self.total_time_label.setStyleSheet("color: #ffffff;")
        time_layout.addWidget(self.current_time_label)
        time_layout.addStretch()
        time_layout.addWidget(self.total_time_label)
        controls_layout.addLayout(time_layout)

        # 播放按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.play_button = QPushButton("▶ 播放")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4a4a4a;
                border: 1px solid #5a5a5a;
                border-radius: 3px;
                padding: 8px 16px;
                color: #ffffff;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a5a5a;
            }
            QPushButton:pressed {
                background-color: #6a6a6a;
            }
        """)
        self.play_button.clicked.connect(lambda: self.toggle_audio_playback(file_path))
        button_layout.addWidget(self.play_button)

        button_layout.addStretch()
        controls_layout.addLayout(button_layout)

        audio_layout.addWidget(controls_frame)

        # 将音频播放器添加到预览区域
        self.preview_scroll.setWidget(audio_widget)

        # 获取音频时长
        self.get_audio_duration(file_path)
    
    def load_text_preview(self, file_path: str):
        """加载文本预览"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # 只读取前1000个字符
                if len(content) == 1000:
                    content += "\n\n... (文件内容过长，仅显示前1000个字符)"
                
                self.preview_label.setText(content)
                self.preview_label.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
                self.preview_label.setWordWrap(True)
                self.preview_label.setStyleSheet("""
                    QLabel {
                        background-color: #2b2b2b;
                        border: 1px solid #5a5a5a;
                        color: #ffffff;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 12px;
                        padding: 10px;
                    }
                """)
        except Exception as e:
            self.preview_label.setText(f"无法读取文本文件: {str(e)}")
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        
        QScrollArea {
            border: 1px solid #5a5a5a;
            border-radius: 5px;
            background-color: #3c3c3c;
        }
        """
        self.setStyleSheet(style)
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)
    
    def mousePressEvent(self, event):
        """单击事件 - 关闭预览"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查点击位置是否在预览区域
            if self.preview_scroll.geometry().contains(event.pos()):
                self.close()
        super().mousePressEvent(event)

    def get_audio_duration(self, file_path: str):
        """获取音频文件时长"""
        try:
            # 使用ffprobe获取音频信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                duration = float(data['format']['duration'])
                self.audio_duration = duration
                self.audio_slider.setMaximum(int(duration))
                self.total_time_label.setText(self.format_time(duration))
            else:
                self.total_time_label.setText("--:--")
        except Exception as e:
            print(f"获取音频时长失败: {e}")
            self.total_time_label.setText("--:--")

    def toggle_audio_playback(self, file_path: str):
        """切换音频播放状态"""
        if self.is_playing:
            self.stop_audio()
        else:
            self.play_audio(file_path)

    def play_audio(self, file_path: str):
        """播放音频"""
        try:
            # 停止当前播放
            if self.audio_process:
                self.audio_process.kill()

            # 使用ffplay播放音频
            cmd = ['ffplay', '-nodisp', '-autoexit', file_path]
            self.audio_process = subprocess.Popen(cmd, stdout=subprocess.DEVNULL,
                                                stderr=subprocess.DEVNULL)

            self.is_playing = True
            self.play_button.setText("⏸ 暂停")
            self.position_timer.start(1000)  # 每秒更新一次位置

        except Exception as e:
            print(f"播放音频失败: {e}")

    def stop_audio(self):
        """停止音频播放"""
        if self.audio_process:
            self.audio_process.kill()
            self.audio_process = None

        self.is_playing = False
        self.play_button.setText("▶ 播放")
        self.position_timer.stop()
        self.audio_position = 0
        self.audio_slider.setValue(0)
        self.current_time_label.setText("00:00")

    def update_position(self):
        """更新播放位置"""
        if self.is_playing and self.audio_process:
            # 检查进程是否还在运行
            if self.audio_process.poll() is not None:
                # 播放结束
                self.stop_audio()
                return

            self.audio_position += 1
            if self.audio_position <= self.audio_duration:
                self.audio_slider.setValue(int(self.audio_position))
                self.current_time_label.setText(self.format_time(self.audio_position))
            else:
                self.stop_audio()

    def on_slider_pressed(self):
        """进度条按下"""
        self.position_timer.stop()

    def on_slider_released(self):
        """进度条释放"""
        if self.is_playing:
            # 重新开始播放（简化实现，实际应该跳转到指定位置）
            self.audio_position = self.audio_slider.value()
            self.current_time_label.setText(self.format_time(self.audio_position))
            self.position_timer.start(1000)

    def format_time(self, seconds: float) -> str:
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def closeEvent(self, event):
        """关闭事件"""
        # 停止音频播放
        self.stop_audio()
        super().closeEvent(event)
