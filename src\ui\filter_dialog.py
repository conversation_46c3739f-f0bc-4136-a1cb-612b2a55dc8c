# -*- coding: utf-8 -*-
"""
筛选对话框 - 文件筛选功能
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QCheckBox, QSlider, QSpinBox, QDateEdit,
                            QFormLayout, QButtonGroup, QLineEdit, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal, QDate
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager
from models.database import DatabaseManager
from datetime import datetime, timedelta


class FilterDialog(QDialog):
    """筛选对话框"""
    
    filter_applied = pyqtSignal(dict)  # 筛选应用信号
    
    def __init__(self, config_manager: Config<PERSON>anager, db_manager: DatabaseManager, 
                 current_category: str = "", parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.current_category = current_category
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("筛选文件")
        self.setFixedSize(350, 450)  # 缩小窗口尺寸
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("筛选条件")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件类型筛选 - 简化版
        type_group = QGroupBox("文件类型")
        type_layout = QVBoxLayout(type_group)

        # 添加全选/全不选按钮
        select_all_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_none_btn = QPushButton("全不选")
        select_all_btn.clicked.connect(self.select_all_types)
        select_none_btn.clicked.connect(self.select_no_types)
        select_all_layout.addWidget(select_all_btn)
        select_all_layout.addWidget(select_none_btn)
        select_all_layout.addStretch()
        type_layout.addLayout(select_all_layout)

        self.type_checkboxes = {}
        file_types = [
            ("图片", ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg", "psd"]),
            ("视频", ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm", "m4v"]),
            ("音频", ["mp3", "wav", "flac", "aac", "ogg", "wma", "m4a"]),
            ("文档", ["txt", "md", "py", "js", "html", "css", "json", "xml", "csv"])
        ]

        for type_name, extensions in file_types:
            checkbox = QCheckBox(type_name)
            checkbox.setChecked(True)  # 默认全选
            self.type_checkboxes[type_name] = {
                'checkbox': checkbox,
                'extensions': extensions
            }
            type_layout.addWidget(checkbox)

        layout.addWidget(type_group)
        
        # 文件大小筛选
        size_group = QGroupBox("文件大小")
        size_layout = QFormLayout(size_group)
        
        # 最小大小
        self.min_size_spin = QSpinBox()
        self.min_size_spin.setRange(0, 999999)
        self.min_size_spin.setSuffix(" KB")
        self.min_size_spin.setValue(0)
        size_layout.addRow("最小大小:", self.min_size_spin)
        
        # 最大大小
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(0, 999999)
        self.max_size_spin.setSuffix(" KB")
        self.max_size_spin.setValue(999999)
        size_layout.addRow("最大大小:", self.max_size_spin)
        
        layout.addWidget(size_group)
        
        # 创建时间筛选
        time_group = QGroupBox("创建时间")
        time_layout = QVBoxLayout(time_group)
        
        # 时间范围选择
        time_range_layout = QHBoxLayout()
        time_range_layout.addWidget(QLabel("时间范围:"))
        
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems([
            "全部时间",
            "今天",
            "最近一周", 
            "最近一个月",
            "最近三个月",
            "最近一年",
            "自定义"
        ])
        time_range_layout.addWidget(self.time_range_combo)
        time_layout.addLayout(time_range_layout)
        
        # 自定义时间范围
        custom_time_layout = QFormLayout()
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setEnabled(False)
        custom_time_layout.addRow("开始日期:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setEnabled(False)
        custom_time_layout.addRow("结束日期:", self.end_date_edit)
        
        time_layout.addLayout(custom_time_layout)
        layout.addWidget(time_group)
        
        # 名称筛选
        name_group = QGroupBox("名称筛选")
        name_layout = QFormLayout(name_group)
        
        self.name_contains_edit = QLineEdit()
        self.name_contains_edit.setPlaceholderText("包含的文字...")
        name_layout.addRow("名称包含:", self.name_contains_edit)
        
        self.name_starts_edit = QLineEdit()
        self.name_starts_edit.setPlaceholderText("开头文字...")
        name_layout.addRow("名称开头:", self.name_starts_edit)
        
        self.name_ends_edit = QLineEdit()
        self.name_ends_edit.setPlaceholderText("结尾文字...")
        name_layout.addRow("名称结尾:", self.name_ends_edit)
        
        layout.addWidget(name_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.reset_btn = QPushButton("重置")
        self.apply_btn = QPushButton("应用筛选")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def setup_connections(self):
        """设置信号连接"""
        self.time_range_combo.currentTextChanged.connect(self.on_time_range_changed)
        self.reset_btn.clicked.connect(self.reset_filters)
        self.apply_btn.clicked.connect(self.apply_filters)
        self.cancel_btn.clicked.connect(self.reject)
    
    def on_time_range_changed(self, range_text: str):
        """时间范围改变"""
        is_custom = (range_text == "自定义")
        self.start_date_edit.setEnabled(is_custom)
        self.end_date_edit.setEnabled(is_custom)
    
    def reset_filters(self):
        """重置筛选条件"""
        # 重置文件类型
        for type_info in self.type_checkboxes.values():
            type_info['checkbox'].setChecked(True)
        
        # 重置文件大小
        self.min_size_spin.setValue(0)
        self.max_size_spin.setValue(999999)
        
        # 重置时间范围
        self.time_range_combo.setCurrentText("全部时间")
        
        # 重置名称筛选
        self.name_contains_edit.clear()
        self.name_starts_edit.clear()
        self.name_ends_edit.clear()
    
    def apply_filters(self):
        """应用筛选条件"""
        filter_criteria = self.get_filter_criteria()
        
        # 验证筛选条件
        if not self.validate_criteria(filter_criteria):
            return
        
        # 发送筛选信号
        self.filter_applied.emit(filter_criteria)
        self.accept()
    
    def get_filter_criteria(self) -> dict:
        """获取筛选条件"""
        criteria = {}
        
        # 文件类型
        selected_types = []
        for type_name, type_info in self.type_checkboxes.items():
            if type_info['checkbox'].isChecked():
                selected_types.extend(type_info['extensions'])
        criteria['file_types'] = selected_types
        
        # 文件大小
        criteria['min_size'] = self.min_size_spin.value() * 1024  # 转换为字节
        criteria['max_size'] = self.max_size_spin.value() * 1024
        
        # 时间范围
        time_range = self.time_range_combo.currentText()
        if time_range == "自定义":
            criteria['start_date'] = self.start_date_edit.date().toPython()
            criteria['end_date'] = self.end_date_edit.date().toPython()
        else:
            criteria['time_range'] = time_range
        
        # 名称筛选
        criteria['name_contains'] = self.name_contains_edit.text().strip()
        criteria['name_starts'] = self.name_starts_edit.text().strip()
        criteria['name_ends'] = self.name_ends_edit.text().strip()
        
        return criteria
    
    def validate_criteria(self, criteria: dict) -> bool:
        """验证筛选条件"""
        # 检查文件大小范围
        if criteria['min_size'] > criteria['max_size']:
            QMessageBox.warning(self, "警告", "最小文件大小不能大于最大文件大小")
            return False
        
        # 检查时间范围
        if 'start_date' in criteria and 'end_date' in criteria:
            if criteria['start_date'] > criteria['end_date']:
                QMessageBox.warning(self, "警告", "开始日期不能晚于结束日期")
                return False
        
        return True
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QComboBox, QLineEdit, QSpinBox, QDateEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        
        QSlider::groove:horizontal {
            border: 1px solid #5a5a5a;
            height: 8px;
            background: #3c3c3c;
            border-radius: 4px;
        }
        
        QSlider::handle:horizontal {
            background: #0078d4;
            border: 1px solid #5a5a5a;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        """
        self.setStyleSheet(style)

    def select_all_types(self):
        """全选文件类型"""
        for type_info in self.type_checkboxes.values():
            type_info['checkbox'].setChecked(True)

    def select_no_types(self):
        """全不选文件类型"""
        for type_info in self.type_checkboxes.values():
            type_info['checkbox'].setChecked(False)
