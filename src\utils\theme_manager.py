# -*- coding: utf-8 -*-
"""
主题管理器 - 管理应用程序的主题和外观
"""

import json
from pathlib import Path
from typing import Dict, Any, List, Optional
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QColor, QPalette
from PyQt6.QtWidgets import QApplication

from utils.config_manager import ConfigManager


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号
    theme_changed = pyqtSignal(str)  # 主题改变信号
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.themes_dir = config_manager.config_dir / "themes"
        self.themes_dir.mkdir(exist_ok=True)
        
        self.current_theme = "dark"  # 默认主题
        self.themes = {}
        
        self.load_builtin_themes()
        self.load_custom_themes()
        
        # 从配置加载当前主题
        saved_theme = config_manager.settings.get("current_theme", "dark")
        if saved_theme in self.themes:
            self.current_theme = saved_theme
    
    def load_builtin_themes(self):
        """加载内置主题"""
        # 暗色主题
        dark_theme = {
            "name": "暗色主题",
            "description": "经典的暗色主题，适合长时间使用",
            "colors": {
                "background": "#2b2b2b",
                "surface": "#3c3c3c",
                "surface_variant": "#4a4a4a",
                "primary": "#0078d4",
                "primary_variant": "#106ebe",
                "secondary": "#5a5a5a",
                "text_primary": "#ffffff",
                "text_secondary": "#cccccc",
                "text_disabled": "#666666",
                "border": "#5a5a5a",
                "border_focus": "#0078d4",
                "hover": "#5a5a5a",
                "pressed": "#6a6a6a",
                "selected": "#0078d4",
                "error": "#ff4444",
                "warning": "#ffaa00",
                "success": "#00aa44"
            },
            "fonts": {
                "default_family": "Microsoft YaHei",
                "default_size": 9,
                "title_size": 12,
                "subtitle_size": 10,
                "caption_size": 8
            },
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15,
                "xlarge": 20
            },
            "borders": {
                "radius": 3,
                "width": 1
            }
        }
        
        # 亮色主题
        light_theme = {
            "name": "亮色主题",
            "description": "清新的亮色主题，适合白天使用",
            "colors": {
                "background": "#ffffff",
                "surface": "#f5f5f5",
                "surface_variant": "#e0e0e0",
                "primary": "#0078d4",
                "primary_variant": "#106ebe",
                "secondary": "#666666",
                "text_primary": "#000000",
                "text_secondary": "#333333",
                "text_disabled": "#999999",
                "border": "#cccccc",
                "border_focus": "#0078d4",
                "hover": "#e0e0e0",
                "pressed": "#d0d0d0",
                "selected": "#0078d4",
                "error": "#cc0000",
                "warning": "#ff8800",
                "success": "#008800"
            },
            "fonts": {
                "default_family": "Microsoft YaHei",
                "default_size": 9,
                "title_size": 12,
                "subtitle_size": 10,
                "caption_size": 8
            },
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15,
                "xlarge": 20
            },
            "borders": {
                "radius": 3,
                "width": 1
            }
        }
        
        # 蓝色主题
        blue_theme = {
            "name": "蓝色主题",
            "description": "专业的蓝色主题，适合设计工作",
            "colors": {
                "background": "#1e2329",
                "surface": "#2d3748",
                "surface_variant": "#4a5568",
                "primary": "#3182ce",
                "primary_variant": "#2c5aa0",
                "secondary": "#718096",
                "text_primary": "#ffffff",
                "text_secondary": "#e2e8f0",
                "text_disabled": "#718096",
                "border": "#4a5568",
                "border_focus": "#3182ce",
                "hover": "#4a5568",
                "pressed": "#5a6578",
                "selected": "#3182ce",
                "error": "#e53e3e",
                "warning": "#d69e2e",
                "success": "#38a169"
            },
            "fonts": {
                "default_family": "Microsoft YaHei",
                "default_size": 9,
                "title_size": 12,
                "subtitle_size": 10,
                "caption_size": 8
            },
            "spacing": {
                "small": 5,
                "medium": 10,
                "large": 15,
                "xlarge": 20
            },
            "borders": {
                "radius": 5,
                "width": 1
            }
        }
        
        self.themes["dark"] = dark_theme
        self.themes["light"] = light_theme
        self.themes["blue"] = blue_theme
    
    def load_custom_themes(self):
        """加载自定义主题"""
        try:
            for theme_file in self.themes_dir.glob("*.json"):
                with open(theme_file, 'r', encoding='utf-8') as f:
                    theme_data = json.load(f)
                
                theme_id = theme_file.stem
                self.themes[theme_id] = theme_data
        except Exception as e:
            print(f"加载自定义主题失败: {e}")
    
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """获取可用主题列表"""
        return self.themes.copy()
    
    def get_current_theme(self) -> Dict[str, Any]:
        """获取当前主题"""
        return self.themes.get(self.current_theme, self.themes["dark"])
    
    def set_theme(self, theme_id: str) -> bool:
        """设置主题"""
        if theme_id not in self.themes:
            return False
        
        self.current_theme = theme_id
        
        # 保存到配置
        self.config_manager.settings["current_theme"] = theme_id
        self.config_manager.save_settings()
        
        # 发送主题改变信号
        self.theme_changed.emit(theme_id)
        
        return True
    
    def generate_stylesheet(self, theme_id: str = None) -> str:
        """生成样式表"""
        if theme_id is None:
            theme_id = self.current_theme
        
        theme = self.themes.get(theme_id, self.themes["dark"])
        colors = theme["colors"]
        fonts = theme["fonts"]
        spacing = theme["spacing"]
        borders = theme["borders"]
        
        # 生成CSS样式
        stylesheet = f"""
        /* 全局样式 */
        QWidget {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            font-family: "{fonts['default_family']}";
            font-size: {fonts['default_size']}pt;
        }}
        
        /* 主窗口 */
        QMainWindow {{
            background-color: {colors['background']};
        }}
        
        /* 标签 */
        QLabel {{
            color: {colors['text_primary']};
            background-color: transparent;
        }}
        
        /* 按钮 */
        QPushButton {{
            background-color: {colors['surface_variant']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            padding: {spacing['small']}px {spacing['medium']}px;
            color: {colors['text_primary']};
            font-weight: bold;
        }}
        
        QPushButton:hover {{
            background-color: {colors['hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['pressed']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['surface']};
            color: {colors['text_disabled']};
        }}
        
        QPushButton:checked {{
            background-color: {colors['primary']};
            border-color: {colors['primary_variant']};
        }}
        
        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors['surface_variant']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            padding: {spacing['small']}px;
            color: {colors['text_primary']};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors['border_focus']};
        }}
        
        /* 列表和表格 */
        QListWidget, QTableWidget, QTreeWidget {{
            background-color: {colors['surface']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            outline: none;
        }}
        
        QListWidget::item, QTableWidget::item, QTreeWidget::item {{
            padding: {spacing['small']}px;
            border-bottom: {borders['width']}px solid {colors['surface_variant']};
            color: {colors['text_primary']};
        }}
        
        QListWidget::item:selected, QTableWidget::item:selected, QTreeWidget::item:selected {{
            background-color: {colors['selected']};
        }}
        
        QListWidget::item:hover, QTableWidget::item:hover, QTreeWidget::item:hover {{
            background-color: {colors['hover']};
        }}
        
        /* 表头 */
        QHeaderView::section {{
            background-color: {colors['surface_variant']};
            color: {colors['text_primary']};
            padding: {spacing['small']}px;
            border: {borders['width']}px solid {colors['border']};
        }}
        
        /* 滚动条 */
        QScrollBar:vertical {{
            background-color: {colors['surface']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['secondary']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['hover']};
        }}
        
        QScrollBar:horizontal {{
            background-color: {colors['surface']};
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {colors['secondary']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['hover']};
        }}
        
        /* 工具栏 */
        QToolBar {{
            background-color: {colors['surface']};
            border: none;
            spacing: {spacing['small']}px;
            padding: {spacing['small']}px;
        }}
        
        QToolBar QToolButton {{
            background-color: {colors['surface_variant']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            padding: {spacing['small']}px {spacing['medium']}px;
            color: {colors['text_primary']};
        }}
        
        QToolBar QToolButton:hover {{
            background-color: {colors['hover']};
        }}
        
        QToolBar QToolButton:pressed {{
            background-color: {colors['pressed']};
        }}
        
        /* 状态栏 */
        QStatusBar {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-top: {borders['width']}px solid {colors['border']};
        }}
        
        /* 分组框 */
        QGroupBox {{
            font-weight: bold;
            border: {borders['width'] * 2}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            margin-top: {spacing['medium']}px;
            padding-top: {spacing['medium']}px;
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: {spacing['medium']}px;
            padding: 0 {spacing['small']}px 0 {spacing['small']}px;
        }}
        
        /* 标签页 */
        QTabWidget::pane {{
            border: {borders['width']}px solid {colors['border']};
            background-color: {colors['surface']};
        }}
        
        QTabBar::tab {{
            background-color: {colors['surface_variant']};
            color: {colors['text_primary']};
            padding: {spacing['small']}px {spacing['medium']}px;
            margin-right: 2px;
            border-top-left-radius: {borders['radius']}px;
            border-top-right-radius: {borders['radius']}px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['primary']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['hover']};
        }}
        
        /* 进度条 */
        QProgressBar {{
            background-color: {colors['surface']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
            text-align: center;
        }}
        
        QProgressBar::chunk {{
            background-color: {colors['primary']};
            border-radius: {borders['radius']}px;
        }}
        
        /* 菜单 */
        QMenu {{
            background-color: {colors['surface']};
            border: {borders['width']}px solid {colors['border']};
            border-radius: {borders['radius']}px;
        }}
        
        QMenu::item {{
            padding: {spacing['small']}px {spacing['medium']}px;
            color: {colors['text_primary']};
        }}
        
        QMenu::item:selected {{
            background-color: {colors['selected']};
        }}
        
        /* 对话框 */
        QDialog {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* 分割器 */
        QSplitter::handle {{
            background-color: {colors['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        """
        
        return stylesheet
    
    def save_custom_theme(self, theme_id: str, theme_data: Dict[str, Any]) -> bool:
        """保存自定义主题"""
        try:
            theme_file = self.themes_dir / f"{theme_id}.json"
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, ensure_ascii=False, indent=2)
            
            self.themes[theme_id] = theme_data
            return True
        except Exception as e:
            print(f"保存主题失败: {e}")
            return False
    
    def delete_custom_theme(self, theme_id: str) -> bool:
        """删除自定义主题"""
        if theme_id in ["dark", "light", "blue"]:
            return False  # 不能删除内置主题
        
        try:
            theme_file = self.themes_dir / f"{theme_id}.json"
            if theme_file.exists():
                theme_file.unlink()
            
            if theme_id in self.themes:
                del self.themes[theme_id]
            
            # 如果删除的是当前主题，切换到默认主题
            if self.current_theme == theme_id:
                self.set_theme("dark")
            
            return True
        except Exception as e:
            print(f"删除主题失败: {e}")
            return False
    
    def apply_theme_to_app(self, app: QApplication = None):
        """将主题应用到应用程序"""
        if app is None:
            app = QApplication.instance()
        
        if app:
            stylesheet = self.generate_stylesheet()
            app.setStyleSheet(stylesheet)
