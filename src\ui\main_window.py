# -*- coding: utf-8 -*-
"""
主窗口 - 应用程序的主界面
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QSplitter, QToolBar, QStatusBar, QPushButton,
                            QLineEdit, QLabel, QFrame, QMenu, QComboBox, QMessageBox, QDialog, QSizePolicy, QApplication, QCheckBox)
from PyQt6.QtCore import Qt, QSize, QTimer
from PyQt6.QtGui import QIcon, QAction, QKeySequence
from pathlib import Path
from typing import Optional

from .category_list import CategoryList
from .file_view import FileView
from .preview_panel import PreviewPanel
from .mini_window import MiniWindow
from .settings_dialog import SettingsDialog
from .import_dialog import ImportDialog
from .export_dialog import ExportDialog
from models.filesystem_manager import FileSystemManager
from utils.config_manager import ConfigManager
from utils.navigation import NavigationManager
from utils.search_sort import SearchSortManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config_manager: ConfigManager, fs_manager: FileSystemManager):
        super().__init__()
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.navigation_manager = NavigationManager(config_manager)
        self.search_sort_manager = SearchSortManager(config_manager, self.fs_manager)
        self.mini_window = None
        self.last_preview_width = 300  # 初始化预览面板宽度

        self.init_ui()
        self.restore_window_state()
        self.setup_navigation()

        # 如果是首次运行，显示欢迎信息
        if self.config_manager.is_first_run():
            self.show_welcome_message()

        # 设置定时清理回收站
        self.setup_auto_cleanup()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧分类列表
        self.category_list = CategoryList(self.config_manager, self.fs_manager)
        splitter.addWidget(self.category_list)

        # 创建中间文件视图
        self.file_view = FileView(self.config_manager, self.fs_manager)
        splitter.addWidget(self.file_view)
        
        # 创建右侧预览面板
        self.preview_panel = PreviewPanel(self.config_manager)
        splitter.addWidget(self.preview_panel)

        # 设置分割器比例
        splitter.setSizes([200, 600, 300])

        # 保存分割器引用以便控制预览面板
        self.main_splitter = splitter
        
        # 连接信号
        self.category_list.category_changed.connect(self.on_category_changed)
        self.category_list.file_dropped.connect(self.on_file_dropped_to_category)
        self.file_view.file_selected.connect(self.preview_panel.show_file_preview)
        self.file_view.folder_entered.connect(self.on_folder_entered)
        self.file_view.category_changed.connect(self.on_file_view_category_changed)
        
        # 创建工具栏和状态栏
        self.create_toolbar()
        self.create_statusbar()

        # 应用样式
        self.apply_styles()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.addToolBar(toolbar)
        
        # 导航按钮
        self.back_action = QAction("←", self)
        self.back_action.setToolTip("返回上一级")
        self.back_action.setEnabled(False)
        self.back_action.triggered.connect(self.go_back)
        toolbar.addAction(self.back_action)

        self.forward_action = QAction("→", self)
        self.forward_action.setToolTip("前进到下一级")
        self.forward_action.setEnabled(False)
        self.forward_action.triggered.connect(self.go_forward)
        toolbar.addAction(self.forward_action)
        
        toolbar.addSeparator()
        
        # 导入按钮
        import_action = QAction("导入", self)
        import_action.setToolTip("导入文件")
        import_action.triggered.connect(self.import_files)
        toolbar.addAction(import_action)

        # 新建文件夹按钮
        new_folder_action = QAction("新建文件夹", self)
        new_folder_action.setToolTip("在当前位置创建新文件夹")
        new_folder_action.triggered.connect(self.create_new_folder)
        toolbar.addAction(new_folder_action)
        
        toolbar.addSeparator()
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)

        # 搜索范围选择
        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems(["全局", "当前分类"])
        self.search_scope_combo.setCurrentText("当前分类")
        self.search_scope_combo.setMaximumWidth(100)
        self.search_scope_combo.currentTextChanged.connect(self.on_search_scope_changed)
        toolbar.addWidget(self.search_scope_combo)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词...")
        self.search_edit.setMaximumWidth(200)
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        toolbar.addWidget(self.search_edit)

        toolbar.addSeparator()

        # PSD文件显示复选框
        self.show_psd_checkbox = QCheckBox("显示PSD文件")
        self.show_psd_checkbox.setChecked(True)  # 默认显示
        self.show_psd_checkbox.setToolTip("显示或隐藏PSD文件")
        self.show_psd_checkbox.toggled.connect(self.on_show_psd_toggled)
        toolbar.addWidget(self.show_psd_checkbox)

        toolbar.addSeparator()

        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setToolTip("导出选中文件")
        export_action.triggered.connect(self.export_files)
        toolbar.addAction(export_action)

        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setToolTip("应用设置")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        # 小窗模式按钮
        mini_action = QAction("小窗模式", self)
        mini_action.setToolTip("切换到小窗模式")
        mini_action.triggered.connect(self.toggle_mini_window)
        toolbar.addAction(mini_action)

        # 添加弹性空间，将后续按钮推到右边
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        toolbar.addWidget(spacer)

        # 预览面板收缩/展开按钮
        self.toggle_preview_action = QAction("◀", self)
        self.toggle_preview_action.setToolTip("收缩/展开预览面板")
        self.toggle_preview_action.triggered.connect(self.toggle_preview_panel)
        toolbar.addAction(self.toggle_preview_action)


    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # 显示就绪状态
        self.statusbar.showMessage("就绪")
    

    def apply_styles(self):
        """应用样式表"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 5px;
            padding: 5px;
        }
        
        QToolBar QToolButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QToolBar QToolButton:hover {
            background-color: #5a5a5a;
        }
        
        QToolBar QToolButton:pressed {
            background-color: #6a6a6a;
        }
        
        QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        pos = self.config_manager.settings["window_position"]
        self.setGeometry(pos["x"], pos["y"], pos["width"], pos["height"])
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        self.statusbar.showMessage("欢迎使用简笔画素材管理软件！")
        self.config_manager.set_first_run_complete()
    
    # 槽函数
    def import_files(self):
        """导入文件"""
        current_category = self.category_list.get_current_category()
        import_dialog = ImportDialog(self.config_manager, self.fs_manager, current_category, self)

        if import_dialog.exec() == QDialog.DialogCode.Accepted:
            # 导入完成后刷新文件视图
            self.file_view.refresh_current_view()
            self.statusbar.showMessage("文件导入完成")
    
    def export_files(self):
        """导出文件"""
        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要导出的文件")
            return

        export_dialog = ExportDialog(self.config_manager, self.fs_manager, selected_files, self)

        if export_dialog.exec() == QDialog.DialogCode.Accepted:
            self.statusbar.showMessage("文件导出完成")
    

    
    def show_settings(self):
        """显示设置对话框"""
        settings_dialog = SettingsDialog(self.config_manager, self)
        settings_dialog.storage_path_changed.connect(self.sync_database_with_new_path)
        settings_dialog.exec()
    
    def toggle_mini_window(self):
        """切换小窗模式"""
        if self.mini_window is None:
            self.mini_window = MiniWindow(self.config_manager, self.fs_manager)
            # 连接小窗关闭信号，当小窗关闭时重新显示主窗口
            self.mini_window.closed.connect(self.on_mini_window_closed)

        if self.mini_window.isVisible():
            self.mini_window.hide()
            self.show()
        else:
            self.hide()
            self.mini_window.show()

    def on_mini_window_closed(self):
        """小窗关闭时的处理"""
        self.show()  # 重新显示主窗口

    def toggle_preview_panel(self):
        """切换预览面板的显示和隐藏"""
        sizes = self.main_splitter.sizes()
        handle = self.main_splitter.handle(2)
        if sizes[2] > 0:  # 如果预览面板可见
            self.last_preview_width = sizes[2]
            self.main_splitter.setSizes([sizes[0], sizes[1] + self.last_preview_width, 0])
            if handle:
                handle.setDisabled(True)
            self.toggle_preview_action.setText("▶")
            self.toggle_preview_action.setToolTip("展开预览面板")
        else:  # 如果预览面板已收缩
            self.main_splitter.setSizes([sizes[0], sizes[1] - self.last_preview_width, self.last_preview_width])
            if handle:
                handle.setDisabled(False)
            self.toggle_preview_action.setText("◀")
            self.toggle_preview_action.setToolTip("收缩预览面板")
    
    def on_search_text_changed(self, text):
        """搜索文本改变"""
        if text.strip():
            # 获取搜索范围
            search_scope = self.search_scope_combo.currentText()

            # 根据搜索范围确定分类
            if search_scope == "全局":
                search_category = None  # 全局搜索
            else:
                search_category = self.category_list.get_current_category()  # 当前分类搜索

            # 执行搜索（支持文件名和类型的模糊搜索）
            search_results = self.search_sort_manager.search_files(text, search_category or "")
            self.file_view.display_search_results(search_results)
        else:
            self.file_view.refresh_current_view()

    def on_search_scope_changed(self, search_scope):
        """搜索范围改变"""
        # 如果有搜索文本，重新搜索
        text = self.search_edit.text()
        if text.strip():
            self.on_search_text_changed(text)

    def on_show_psd_toggled(self, checked: bool):
        """PSD文件显示/隐藏切换"""
        # 设置文件视图的PSD显示状态
        self.file_view.set_show_psd_files(checked)
        # 刷新当前视图
        self.file_view.refresh_current_view()



    def _merge_search_results(self, list1, list2):
        """合并搜索结果并去重"""
        seen = set()
        merged = []

        for item in list1 + list2:
            item_id = item.get('id') or item.get('path', '')
            if item_id not in seen:
                seen.add(item_id)
                merged.append(item)

        return merged
    
    def rename_selected(self):
        """重命名选中项"""
        self.file_view.rename_selected()
    
    def focus_search(self):
        """聚焦搜索框"""
        self.search_edit.setFocus()
        self.search_edit.selectAll()


    
    def on_search_file_selected(self, file_info: dict):
        """处理从搜索对话框中选择的文件"""
        category = file_info.get('category')
        if category:
            self.switch_to_category(category)
            self.file_view.select_file(file_info)

    def sync_database_with_new_path(self, old_path: Path, new_path: str):
        """同步数据库中的文件路径"""
        reply = QMessageBox.question(
            self, "确认更新路径",
            "存储路径已更改。是否要更新数据库中的所有文件路径？\n"
            "这可能需要一些时间，并且在完成前无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            # 文件系统管理器不需要更新路径，因为它直接基于文件系统
            # 只需要刷新视图即可
            updated_count = 0  # 文件系统管理器不需要路径更新
            QMessageBox.information(self, "更新完成", f"已成功更新 {updated_count} 个文件路径。")
            self.file_view.refresh_current_view()

    def select_all(self):
        """全选"""
        self.file_view.select_all()

    def setup_navigation(self):
        """设置导航功能"""
        self.update_navigation_buttons()

    def on_category_changed(self, category: str):
        """分类改变事件"""
        # 添加到导航历史
        self.navigation_manager.add_location(category)

        # 切换文件视图
        self.file_view.set_category(category)

        # 更新导航按钮状态
        self.update_navigation_buttons()

    def on_file_view_category_changed(self, category: str, subfolder: str):
        """文件视图分类切换事件（用于定位功能）"""
        # 更新分类列表的选中状态
        self.category_list.set_current_category(category, emit_signal=False)

        # 添加到导航历史
        location = f"{category}/{subfolder}" if subfolder else category
        self.navigation_manager.add_location(location)

        # 更新导航按钮状态
        self.update_navigation_buttons()

        # 更新状态栏
        location_text = f"{category}/{subfolder}" if subfolder else category
        self.statusbar.showMessage(f"当前位置: {location_text}")

    def go_back(self):
        """后退导航"""
        location = self.navigation_manager.go_back()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def go_forward(self):
        """前进导航"""
        location = self.navigation_manager.go_forward()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        self.back_action.setEnabled(self.navigation_manager.can_go_back())
        self.forward_action.setEnabled(self.navigation_manager.can_go_forward())

    def on_folder_entered(self, category: str, subfolder: str):
        """文件夹进入事件处理"""
        # 添加到导航历史
        self.navigation_manager.add_location(category, subfolder)
        # 更新导航按钮状态
        self.update_navigation_buttons()



    def go_up_level(self):
        """返回上一级目录"""
        current_subfolder = self.file_view.current_subfolder
        if current_subfolder:
            # 如果在子文件夹中，返回上一级
            parent_folder = str(Path(current_subfolder).parent)
            if parent_folder == ".":
                parent_folder = ""

            self.file_view.set_category(self.file_view.current_category, parent_folder)

            # 添加到导航历史
            self.navigation_manager.add_location(
                self.file_view.current_category,
                parent_folder
            )
            self.update_navigation_buttons()

    def closeEvent(self, event):
        """关闭事件"""
        # 保存窗口状态
        geometry = self.geometry()
        self.config_manager.settings["window_position"] = {
            "x": geometry.x(),
            "y": geometry.y(),
            "width": geometry.width(),
            "height": geometry.height()
        }
        self.config_manager.save_settings()

        # 关闭小窗口
        if self.mini_window:
            self.mini_window.close()

        event.accept()

    # 新增的方法
    def switch_to_category(self, category: str):
        """快速切换到指定分类"""
        self.category_list.set_current_category(category, emit_signal=True)

    def create_new_folder(self):
        """调用文件视图的创建文件夹功能"""
        self.file_view.create_new_folder()





    def setup_auto_cleanup(self):
        """设置自动清理回收站"""
        # 创建定时器，每天检查一次
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.auto_cleanup_recycle_bin)

        # 设置为24小时（86400000毫秒）
        self.cleanup_timer.start(24 * 60 * 60 * 1000)

        # 启动时也执行一次清理
        self.auto_cleanup_recycle_bin()

    def auto_cleanup_recycle_bin(self):
        """自动清理回收站"""
        try:
            # 对于文件系统管理器，我们需要实现自动清理逻辑
            # 获取回收站文件并检查删除时间
            deleted_files = self.fs_manager.get_deleted_files()
            old_files = []

            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=30)

            for file_info in deleted_files:
                delete_time_str = file_info.get('delete_time')
                if delete_time_str:
                    try:
                        delete_time = datetime.fromisoformat(delete_time_str)
                        if delete_time < cutoff_date:
                            old_files.append(file_info)
                    except ValueError:
                        continue

            if old_files:
                # 执行清理 - 删除文件和元数据
                deleted_count = 0
                for file_info in old_files:
                    try:
                        import os
                        file_path = file_info['path']
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        print(f"删除文件失败 {file_info['name']}: {e}")

                # 清理元数据
                self.fs_manager.cleanup_non_existent_files()

                if deleted_count > 0:
                    print(f"自动清理回收站：删除了 {deleted_count} 个超过30天的文件")

                    # 如果当前在回收站页面，刷新视图
                    if self.category_list.get_current_category() == "回收站":
                        self.file_view.refresh_current_view()

        except Exception as e:
            print(f"自动清理回收站失败: {e}")

    def copy_selected(self):
        """复制选中的文件"""
        self.file_view.copy_selected()

    def paste_files(self):
        """粘贴文件"""
        self.file_view.paste_files()

    def clear_selection(self):
        """清除所有选择"""
        self.file_view.clear_all_selections()

    def on_file_dropped_to_category(self, file_data: str, target_category: str):
        """文件拖拽到分类栏的处理"""
        try:
            import json
            file_info = json.loads(file_data)

            print(f"处理文件拖拽到分类: {file_info.get('name', '未知')} -> {target_category}")

            # 获取拖放操作类型
            modifiers = QApplication.keyboardModifiers()
            if modifiers & Qt.KeyboardModifier.ControlModifier:
                # Ctrl+拖拽 = 复制
                drop_action = Qt.DropAction.CopyAction
                action_text = "复制"
            else:
                # 默认 = 移动
                drop_action = Qt.DropAction.MoveAction
                action_text = "移动"

            # 记录当前分类，用于后续刷新
            current_category = self.file_view.current_category

            # 检查是否是同一分类
            source_category = file_info.get('category', current_category)
            if source_category == target_category and drop_action == Qt.DropAction.MoveAction:
                self.statusbar.showMessage("文件已在目标分类中")
                return

            # 使用拖拽管理器处理文件移动
            success = self.file_view.drag_drop_manager.handle_internal_drop(
                self._create_mime_data_from_file_info(file_info),
                target_category,
                "",  # 空子文件夹
                drop_action
            )

            if success:
                self.statusbar.showMessage(f"文件已{action_text}到 {target_category}")

                # 延迟刷新界面以确保文件系统操作完成
                QTimer.singleShot(100, self._refresh_views_after_operation)

                # 如果是移动操作且移动到了不同的分类，立即刷新当前视图
                if (drop_action == Qt.DropAction.MoveAction and
                    source_category != target_category and
                    current_category == source_category):
                    QTimer.singleShot(50, self.file_view.refresh_current_view)

                # 如果目标分类不是当前分类，可以选择切换到目标分类
                if target_category != current_category:
                    # 询问用户是否要切换到目标分类
                    reply = QMessageBox.question(
                        self, "切换分类",
                        f"文件已{action_text}到 {target_category}。是否要切换到该分类查看？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.No
                    )
                    if reply == QMessageBox.StandardButton.Yes:
                        self.category_list.set_current_category(target_category, emit_signal=True)
            else:
                self.statusbar.showMessage(f"文件{action_text}失败")

        except Exception as e:
            print(f"拖拽到分类失败: {e}")
            self.statusbar.showMessage("文件操作失败")

    def _create_mime_data_from_file_info(self, file_info: dict):
        """从文件信息创建MIME数据"""
        from PyQt6.QtCore import QMimeData
        import json

        mime_data = QMimeData()
        file_data = json.dumps(file_info).encode('utf-8')
        mime_data.setData("application/x-file-info", file_data)
        return mime_data

    def _refresh_views_after_operation(self):
        """操作完成后刷新视图"""
        # 刷新当前文件视图
        self.file_view.refresh_current_view()
        # 刷新分类列表（如果有文件数量显示）
        self.category_list.refresh_categories()


