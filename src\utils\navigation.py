# -*- coding: utf-8 -*-
"""
导航历史管理器 - 管理浏览历史和前进后退功能
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from utils.config_manager import ConfigManager


class NavigationManager:
    """导航历史管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.history: List[Dict[str, Any]] = []
        self.current_index = -1
        self.max_history = config_manager.settings.get("navigation_history_limit", 30)
        
        # 从配置文件加载历史记录
        self.load_history()
    
    def load_history(self):
        """从配置文件加载历史记录"""
        nav_data = self.config_manager.navigation
        self.history = nav_data.get("history", [])
        self.current_index = nav_data.get("current_index", -1)
        
        # 确保索引有效
        if self.current_index >= len(self.history):
            self.current_index = len(self.history) - 1
    
    def save_history(self):
        """保存历史记录到配置文件"""
        self.config_manager.navigation["history"] = self.history
        self.config_manager.navigation["current_index"] = self.current_index
        self.config_manager.save_navigation()
    
    def add_location(self, category: str, subfolder: str = "", title: str = None):
        """
        添加新的位置到历史记录
        
        Args:
            category: 分类名称
            subfolder: 子文件夹路径
            title: 显示标题（可选）
        """
        # 构建路径
        if subfolder:
            path = f"/{category}/{subfolder}"
        else:
            path = f"/{category}"
        
        # 创建历史记录项
        location = {
            "category": category,
            "subfolder": subfolder,
            "path": path,
            "title": title or path,
            "timestamp": datetime.now().isoformat()
        }
        
        # 如果当前位置不是历史记录的最后一项，则删除后面的记录
        if self.current_index < len(self.history) - 1:
            self.history = self.history[:self.current_index + 1]
        
        # 检查是否与当前位置相同
        if (self.history and 
            self.current_index >= 0 and 
            self.history[self.current_index]["path"] == path):
            return  # 相同位置，不添加
        
        # 添加新位置
        self.history.append(location)
        self.current_index = len(self.history) - 1
        
        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]
            self.current_index = len(self.history) - 1
        
        # 保存到配置文件
        self.save_history()
    
    def can_go_back(self) -> bool:
        """检查是否可以后退"""
        return self.current_index > 0
    
    def can_go_forward(self) -> bool:
        """检查是否可以前进"""
        return self.current_index < len(self.history) - 1
    
    def go_back(self) -> Optional[Dict[str, Any]]:
        """后退到上一个位置"""
        if self.can_go_back():
            self.current_index -= 1
            self.save_history()
            return self.history[self.current_index]
        return None
    
    def go_forward(self) -> Optional[Dict[str, Any]]:
        """前进到下一个位置"""
        if self.can_go_forward():
            self.current_index += 1
            self.save_history()
            return self.history[self.current_index]
        return None
    
    def get_current_location(self) -> Optional[Dict[str, Any]]:
        """获取当前位置"""
        if 0 <= self.current_index < len(self.history):
            return self.history[self.current_index]
        return None
    
    def get_back_list(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取后退历史列表"""
        if self.current_index <= 0:
            return []
        
        start_index = max(0, self.current_index - count)
        return self.history[start_index:self.current_index]
    
    def get_forward_list(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取前进历史列表"""
        if self.current_index >= len(self.history) - 1:
            return []
        
        end_index = min(len(self.history), self.current_index + count + 1)
        return self.history[self.current_index + 1:end_index]
    
    def clear_history(self):
        """清空历史记录"""
        self.history.clear()
        self.current_index = -1
        self.save_history()
    
    def remove_invalid_locations(self, valid_categories: List[str]):
        """移除无效的位置记录"""
        original_length = len(self.history)
        
        # 过滤有效的历史记录
        valid_history = []
        for location in self.history:
            if location.get("category") in valid_categories:
                valid_history.append(location)
        
        self.history = valid_history
        
        # 调整当前索引
        if self.current_index >= len(self.history):
            self.current_index = len(self.history) - 1
        
        # 如果有变化，保存配置
        if len(self.history) != original_length:
            self.save_history()
    
    def get_recent_locations(self, count: int = 5) -> List[Dict[str, Any]]:
        """获取最近访问的位置"""
        if not self.history:
            return []
        
        # 按时间戳排序，获取最近的位置
        sorted_history = sorted(
            self.history, 
            key=lambda x: x.get("timestamp", ""), 
            reverse=True
        )
        
        # 去重（相同路径只保留一个）
        seen_paths = set()
        recent_locations = []
        
        for location in sorted_history:
            path = location.get("path", "")
            if path not in seen_paths:
                seen_paths.add(path)
                recent_locations.append(location)
                
                if len(recent_locations) >= count:
                    break
        
        return recent_locations
    
    def jump_to_location(self, category: str, subfolder: str = "") -> Dict[str, Any]:
        """
        跳转到指定位置（不添加到历史记录）
        
        Args:
            category: 分类名称
            subfolder: 子文件夹路径
            
        Returns:
            Dict: 位置信息
        """
        path = f"/{category}/{subfolder}" if subfolder else f"/{category}"
        
        location = {
            "category": category,
            "subfolder": subfolder,
            "path": path,
            "title": path,
            "timestamp": datetime.now().isoformat()
        }
        
        return location
