# -*- coding: utf-8 -*-
"""
设置对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFileDialog, QSpinBox,
                            QGroupBox, QFormLayout, QTabWidget, QWidget,
                            QCheckBox, QComboBox, QMessageBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from pathlib import Path
import os

from utils.config_manager import ConfigManager


class SettingsDialog(QDialog):
    """设置对话框"""
    storage_path_changed = pyqtSignal(Path, str)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.settings = config_manager.settings.copy()  # 创建设置的副本
        
        self.init_ui()
        self.load_current_settings()
        self.apply_styles()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("全局设置")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 常规设置选项卡
        self.create_general_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_settings)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def create_general_tab(self):
        """创建常规设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 存储设置组
        storage_group = QGroupBox("存储设置")
        storage_layout = QFormLayout(storage_group)
        
        # 存储路径
        storage_layout_h = QHBoxLayout()
        self.storage_path_edit = QLineEdit()
        self.storage_path_edit.setReadOnly(True)
        storage_layout_h.addWidget(self.storage_path_edit)
        
        browse_storage_btn = QPushButton("浏览...")
        browse_storage_btn.clicked.connect(self.browse_storage_path)
        storage_layout_h.addWidget(browse_storage_btn)
        
        storage_layout.addRow("存储位置:", storage_layout_h)
        
        layout.addWidget(storage_group)
        
        # 缩略图设置组
        thumbnail_group = QGroupBox("缩略图设置")
        thumbnail_layout = QFormLayout(thumbnail_group)
        
        # 缩略图大小
        self.thumbnail_size_spin = QSpinBox()
        self.thumbnail_size_spin.setRange(64, 512)
        self.thumbnail_size_spin.setSuffix(" px")
        self.thumbnail_size_spin.setValue(128)
        thumbnail_layout.addRow("缩略图大小:", self.thumbnail_size_spin)
        
        # 缩略图质量
        self.thumbnail_quality_combo = QComboBox()
        self.thumbnail_quality_combo.addItems(["低", "中", "高"])
        self.thumbnail_quality_combo.setCurrentText("中")
        thumbnail_layout.addRow("缩略图质量:", self.thumbnail_quality_combo)
        
        layout.addWidget(thumbnail_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "常规")
    
    def create_appearance_tab(self):
        """创建外观设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 主题设置组
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题"])
        self.theme_combo.setCurrentText("深色主题")
        theme_layout.addRow("主题:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 字体设置组
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(font_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "外观")
    
    def create_advanced_tab(self):
        """创建高级设置选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 性能设置组
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        # 启用缓存
        self.enable_cache_check = QCheckBox()
        self.enable_cache_check.setChecked(True)
        performance_layout.addRow("启用缓存:", self.enable_cache_check)
        
        # 最大缓存大小
        self.max_cache_size_spin = QSpinBox()
        self.max_cache_size_spin.setRange(100, 2000)
        self.max_cache_size_spin.setSuffix(" MB")
        self.max_cache_size_spin.setValue(500)
        performance_layout.addRow("最大缓存大小:", self.max_cache_size_spin)
        
        layout.addWidget(performance_group)
        
        # 回收站设置组
        recycle_group = QGroupBox("回收站设置")
        recycle_layout = QFormLayout(recycle_group)
        
        # 自动清理天数
        self.auto_clean_days_spin = QSpinBox()
        self.auto_clean_days_spin.setRange(1, 365)
        self.auto_clean_days_spin.setValue(30)
        self.auto_clean_days_spin.setSuffix(" 天")
        recycle_layout.addRow("自动清理天数:", self.auto_clean_days_spin)
        
        layout.addWidget(recycle_group)
        
        layout.addStretch()
        self.tab_widget.addTab(tab, "高级")
    
    def load_current_settings(self):
        """加载当前设置"""
        # 存储路径
        storage_path = self.settings.get("storage_path", "")
        self.storage_path_edit.setText(storage_path)
        
        # 缩略图设置
        thumbnail_size = self.settings.get("thumbnail_size", 128)
        self.thumbnail_size_spin.setValue(thumbnail_size)
        
        thumbnail_quality = self.settings.get("thumbnail_quality", "中")
        self.thumbnail_quality_combo.setCurrentText(thumbnail_quality)
        
    def browse_storage_path(self):
        """浏览存储路径"""
        current_path = self.storage_path_edit.text()
        if not current_path:
            current_path = str(Path.home())
        
        path = QFileDialog.getExistingDirectory(
            self, "选择存储位置", current_path
        )
        
        if path:
            self.storage_path_edit.setText(path)
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        # 获取默认设置
        default_settings = self.config_manager._default_settings
        
        # 应用默认设置到UI
        self.storage_path_edit.setText(default_settings.get("storage_path", ""))
        self.thumbnail_size_spin.setValue(default_settings.get("thumbnail_size", 128))
        self.thumbnail_quality_combo.setCurrentText(default_settings.get("thumbnail_quality", "中"))
        
        QMessageBox.information(self, "提示", "已重置为默认设置，点击“确定”保存。")
    
    def accept_settings(self):
        """接受并保存设置"""
        old_storage_path = self.config_manager.get_storage_path()
        new_storage_path = self.storage_path_edit.text()

        # 保存常规设置
        self.settings["storage_path"] = new_storage_path
        self.settings["thumbnail_size"] = self.thumbnail_size_spin.value()
        self.settings["thumbnail_quality"] = self.thumbnail_quality_combo.currentText()
        
        self.config_manager.settings = self.settings
        self.config_manager.save_settings()

        # 如果存储路径已更改，则同步数据库
        if old_storage_path != Path(new_storage_path):
            self.storage_path_changed.emit(old_storage_path, new_storage_path)

        self.accept()
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #5a5a5a;
            background-color: #3c3c3c;
        }
        
        QTabWidget::tab-bar {
            alignment: center;
        }
        
        QTabBar::tab {
            background-color: #4a4a4a;
            color: #ffffff;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        
        QTabBar::tab:selected {
            background-color: #3c3c3c;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #5a5a5a;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:default {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        
        QPushButton:default:hover {
            background-color: #106ebe;
        }
        
        QLineEdit, QSpinBox, QComboBox {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
            border-color: #0078d4;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            background-color: #3c3c3c;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        """
        self.setStyleSheet(style)
