# 简笔画素材管理软件

一款专为设计师、剪辑师、创作者打造的素材管理工具，提供比传统文件管理器更强大的功能和更好的用户体验。

## 🌟 主要特性

- **分类管理**: 五大主分类（人物、场景、道具、其他、回收站），支持子文件夹
- **可视化预览**: 缩略图显示，支持图片、视频、音频等多种格式
- **拖拽操作**: 支持文件自由拖动、跨分类拖动、拖拽至其他软件
- **搜索功能**: 全局搜索或限定分类搜索，支持实时模糊搜索
- **小窗模式**: 置顶小窗口，便于拖拽至其他软件使用
- **导出功能**: 支持导出至剪映草稿或普通文件夹
- **自定义外观**: 分类标签、文件夹、文件名称颜色自定义
- **导航历史**: 支持前进/后退导航，记录浏览历史

## 🚀 快速开始

### 环境要求

- Python 3.11.8 或更高版本
- Windows 10/11 或 macOS 10.15+

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd 简笔画素材管理

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 运行程序

```bash
# 方式1：直接运行主程序
python main.py

# 方式2：使用启动脚本
python run.py
```

## 📁 项目结构

```
简笔画素材管理/
├── src/                    # 源代码目录
│   ├── ui/                # 用户界面组件
│   │   ├── main_window.py # 主窗口
│   │   ├── category_list.py # 分类列表
│   │   ├── file_view.py   # 文件视图
│   │   ├── preview_panel.py # 预览面板
│   │   └── mini_window.py # 小窗模式
│   ├── models/            # 数据模型
│   │   └── database.py    # 数据库管理
│   └── utils/             # 工具模块
│       ├── config_manager.py # 配置管理
│       └── file_operations.py # 文件操作
├── tests/                 # 测试文件
├── docs/                  # 文档
├── main.py               # 主程序入口
├── run.py                # 启动脚本
├── requirements.txt      # 依赖列表
├── pyproject.toml        # 项目配置
└── README.md            # 项目说明
```

## 🎯 功能说明

### 分类管理
- **人物分类**: 仅允许图片格式，默认子文件夹：主角、路人、怪兽、其他
- **场景分类**: 仅允许图片格式，默认子文件夹：室内、室外
- **道具分类**: 仅允许图片格式，默认子文件夹：武器、物品、载具
- **其他分类**: 允许所有格式，默认子文件夹：BGM、音效、视频、文本、其他
- **回收站**: 存放删除的文件，30天后自动清理

### 快捷键
| 快捷键 | 功能 |
|--------|------|
| F2 | 重命名 |
| Ctrl+F | 搜索栏 |
| Ctrl+A | 全选 |
| Ctrl+1-5 | 快速切换分类 |
| Alt+Space+X | 切换小窗模式 |
| Backspace | 返回上一级 |
| Alt+左箭头 | 返回上一级 |
| Alt+右箭头 | 前进到下一级 |

## 🛠️ 开发计划

项目采用迭代开发模式，分为四个主要里程碑：

### 里程碑一：基础框架与核心文件系统 ✅
- [x] 环境搭建与项目初始化
- [x] 主窗口UI框架
- [x] 分类系统（静态）
- [x] 文件存储与数据库
- [x] 文件视图

### 里程碑二：核心交互功能完善 🚧
- [ ] 文件拖放功能
- [ ] 文件预览功能
- [ ] 搜索与排序
- [ ] 文件基础操作

### 里程碑三：高级功能与个性化 📋
- [ ] 小窗模式
- [ ] 回收站管理
- [ ] 导航历史功能
- [ ] 导出到剪映
- [ ] 自定义外观

### 里程碑四：优化、测试与打包 📋
- [ ] 性能优化
- [ ] UI/UX优化
- [ ] 全面测试
- [ ] 打包与发布

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！软件

本项目采用 PyQt6 开发跨平台 GUI 应用，用于分类、管理简笔画图片、音视频等素材。

## 安装依赖

```bash
python -m pip install --upgrade pip
pip install -e .
```

> 依赖定义于 `pyproject.toml` 中，建议在 **Python 3.11** 虚拟环境中执行。

## 运行应用

```bash
python -m simple_sketch_manager
```

## 目录结构

```
简笔画素材管理/
├── pyproject.toml
├── README.md
└── src/
    └── simple_sketch_manager/
        ├── __init__.py
        ├── main.py
        ├── ui/
        │   ├── __init__.py
        │   └── main_window.py
        ├── models/
        │   ├── __init__.py
        │   └── database.py
        └── utils/
            └── __init__.py
```

## 许可证

MIT License 