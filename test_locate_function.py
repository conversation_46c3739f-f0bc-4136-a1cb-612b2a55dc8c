#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定位功能的脚本
"""

import sys
import os
from pathlib import Path

def test_locate_function():
    """测试定位功能是否正确实现"""
    print("检查定位功能实现...")
    print("=" * 50)
    
    try:
        # 检查文件视图代码
        file_view_code = open("src/ui/file_view.py", "r", encoding="utf-8").read()
        
        # 检查定位方法
        if "def locate_selected_file(self):" in file_view_code:
            print("✓ locate_selected_file 方法存在")
        else:
            print("✗ locate_selected_file 方法不存在")
            return False
            
        # 检查选中文件方法
        if "def _select_file_by_info(self, target_file_info: dict):" in file_view_code:
            print("✓ _select_file_by_info 方法存在")
        else:
            print("✗ _select_file_by_info 方法不存在")
            return False
            
        # 检查是否正确设置分类
        if "self.set_category(category, subfolder)" in file_view_code:
            print("✓ 正确调用 set_category 方法")
        else:
            print("✗ 未找到 set_category 调用")
            return False
            
        # 检查是否退出搜索模式
        if "self.is_search_mode = False" in file_view_code:
            print("✓ 正确退出搜索模式")
        else:
            print("✗ 未正确退出搜索模式")
            return False
            
        # 检查是否有延迟选中
        if "QTimer.singleShot(100, lambda: self._select_file_by_info(file_info))" in file_view_code:
            print("✓ 正确实现延迟选中")
        else:
            print("✗ 未找到延迟选中逻辑")
            return False

        # 检查主窗口信号处理
        main_window_code = open("src/ui/main_window.py", "r", encoding="utf-8").read()

        if "self.file_view.category_changed.connect(self.on_file_view_category_changed)" in main_window_code:
            print("✓ 正确连接文件视图分类切换信号")
        else:
            print("✗ 未找到文件视图分类切换信号连接")
            return False

        if "def on_file_view_category_changed(self, category: str, subfolder: str):" in main_window_code:
            print("✓ 正确实现文件视图分类切换处理方法")
        else:
            print("✗ 未找到文件视图分类切换处理方法")
            return False

        return True
        
    except Exception as e:
        print(f"✗ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试定位功能...")
    
    # 测试定位功能
    if not test_locate_function():
        return False
    
    print()
    print("=" * 50)
    print("✓ 定位功能检查通过")
    print()
    print("定位功能工作流程:")
    print("1. 用户在搜索结果中右键点击文件")
    print("2. 选择'📍 定位文件'菜单项")
    print("3. 系统解析文件路径，获取分类和子文件夹")
    print("4. 退出搜索模式")
    print("5. 切换到目标分类和子文件夹")
    print("6. 发送分类切换信号给主窗口")
    print("7. 延迟选中目标文件")
    print("8. 显示定位成功消息")
    print()
    print("如果定位后仍然显示搜索状态，请检查:")
    print("- 主窗口是否正确响应 category_changed 信号")
    print("- 分类列表是否正确更新选中状态")
    print("- 面包屑导航是否正确更新")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
