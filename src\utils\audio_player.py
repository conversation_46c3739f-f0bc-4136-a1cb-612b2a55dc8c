"""
简单的音频播放器
支持基本的播放、暂停、跳转功能
"""

import subprocess
import os
import json
from pathlib import Path
from PyQt6.QtCore import QObject, QTimer, pyqtSignal


class SimpleAudioPlayer(QObject):
    """简单的音频播放器"""
    
    # 信号
    position_changed = pyqtSignal(float)  # 位置改变信号
    duration_changed = pyqtSignal(float)  # 时长改变信号
    playback_finished = pyqtSignal()  # 播放完成信号
    
    def __init__(self):
        super().__init__()
        self.process = None
        self.file_path = ""
        self.duration = 0
        self.position = 0
        self.is_playing = False
        
        # 位置更新定时器
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self._update_position)
        
    def load_file(self, file_path: str):
        """加载音频文件"""
        self.file_path = file_path
        self.duration = self._get_duration(file_path)
        self.position = 0
        self.duration_changed.emit(self.duration)
        
    def play(self, start_position: float = 0):
        """播放音频"""
        if not self.file_path:
            return False
            
        try:
            # 停止当前播放
            self.stop()
            
            # 构建播放命令
            cmd = ['ffplay', '-nodisp', '-autoexit', '-hide_banner', '-loglevel', 'quiet']
            if start_position > 0:
                cmd.extend(['-ss', str(start_position)])
            cmd.append(self.file_path)
            
            # 启动播放进程
            self.process = subprocess.Popen(cmd, 
                                          stdout=subprocess.DEVNULL, 
                                          stderr=subprocess.DEVNULL)
            
            self.position = start_position
            self.is_playing = True
            self.position_timer.start(1000)  # 每秒更新一次
            
            return True
            
        except Exception as e:
            print(f"播放失败: {e}")
            return False
    
    def pause(self):
        """暂停播放"""
        if self.process and self.is_playing:
            try:
                self.process.terminate()
                self.process.wait(timeout=1)
            except:
                try:
                    self.process.kill()
                except:
                    pass
            self.process = None
            self.is_playing = False
            self.position_timer.stop()
    
    def stop(self):
        """停止播放"""
        self.pause()
        self.position = 0
        self.position_changed.emit(self.position)
    
    def seek(self, position: float):
        """跳转到指定位置"""
        if not self.file_path:
            return
            
        was_playing = self.is_playing
        self.pause()
        self.position = position
        self.position_changed.emit(self.position)
        
        if was_playing:
            self.play(position)
    
    def get_position(self) -> float:
        """获取当前位置"""
        return self.position
    
    def get_duration(self) -> float:
        """获取总时长"""
        return self.duration
    
    def is_playing_audio(self) -> bool:
        """是否正在播放"""
        return self.is_playing
    
    def _update_position(self):
        """更新播放位置"""
        if self.is_playing and self.process:
            # 检查进程是否还在运行
            if self.process.poll() is not None:
                # 播放结束
                self.is_playing = False
                self.position_timer.stop()
                self.process = None
                self.playback_finished.emit()
                return
            
            # 更新位置
            self.position += 1
            if self.position > self.duration:
                self.position = self.duration
                self.is_playing = False
                self.position_timer.stop()
                self.playback_finished.emit()
            else:
                self.position_changed.emit(self.position)
    
    def _get_duration(self, file_path: str) -> float:
        """获取音频文件时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                return float(data['format']['duration'])
        except Exception as e:
            print(f"获取音频时长失败: {e}")
        return 0
    
    def cleanup(self):
        """清理资源"""
        self.stop()
        if self.process:
            try:
                self.process.kill()
            except:
                pass
            self.process = None
