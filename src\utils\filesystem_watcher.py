# -*- coding: utf-8 -*-
"""
文件系统监视器 - 监视文件系统变化并更新元数据
"""

import time
from pathlib import Path
from typing import Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class FileSystemEventHandler(FileSystemEventHandler):
    """文件系统事件处理器"""
    
    def __init__(self, fs_manager):
        """
        初始化事件处理器
        
        Args:
            fs_manager: 文件系统管理器实例
        """
        super().__init__()
        self.fs_manager = fs_manager
        
    def on_created(self, event):
        """文件创建事件"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        if file_path.name.startswith('.'):
            return
            
        # 确定分类
        category = self._get_category_from_path(file_path)
        if category:
            # 添加文件记录
            file_type = file_path.suffix.lower().lstrip('.') or 'file'
            self.fs_manager.add_file(
                name=file_path.name,
                path=str(file_path),
                category=category,
                file_type=file_type
            )
            print(f"文件已添加: {file_path.name} -> {category}")
    
    def on_deleted(self, event):
        """文件删除事件"""
        if event.is_directory:
            return
            
        file_path = Path(event.src_path)
        if file_path.name.startswith('.'):
            return
            
        # 清理元数据中的记录
        self.fs_manager.cleanup_non_existent_files()
        print(f"文件已删除: {file_path.name}")
    
    def on_moved(self, event):
        """文件移动事件"""
        if event.is_directory:
            return
            
        src_path = Path(event.src_path)
        dest_path = Path(event.dest_path)
        
        if src_path.name.startswith('.') or dest_path.name.startswith('.'):
            return
            
        # 获取源和目标分类
        src_category = self._get_category_from_path(src_path)
        dest_category = self._get_category_from_path(dest_path)
        
        if src_category and dest_category:
            # 获取文件ID
            file_id = self.fs_manager._get_file_id(str(src_path))
            
            # 更新文件位置
            self.fs_manager.update_file_location(
                file_id=file_id,
                new_path=str(dest_path),
                new_category=dest_category
            )
            print(f"文件已移动: {src_path.name} -> {dest_category}")
    
    def on_modified(self, event):
        """文件修改事件"""
        # 对于基于文件系统的管理器，修改事件不需要特别处理
        # 文件信息会在下次访问时自动更新
        pass
    
    def _get_category_from_path(self, file_path: Path) -> Optional[str]:
        """从文件路径获取分类"""
        try:
            # 获取相对于存储根目录的路径
            relative_path = file_path.relative_to(self.fs_manager.storage_path)
            
            # 第一级目录就是分类
            category = relative_path.parts[0]
            
            # 检查是否是有效分类
            if category in self.fs_manager.categories:
                return category
        except (ValueError, IndexError):
            pass
        
        return None


def start_watching(fs_manager, storage_path: Path) -> Observer:
    """
    启动文件系统监视器
    
    Args:
        fs_manager: 文件系统管理器实例
        storage_path: 要监视的存储路径
        
    Returns:
        Observer: 监视器实例
    """
    event_handler = FileSystemEventHandler(fs_manager)
    observer = Observer()
    
    # 监视存储根目录
    observer.schedule(event_handler, str(storage_path), recursive=True)
    
    # 启动监视器
    observer.start()
    print(f"文件系统监视器已启动，监视路径: {storage_path}")
    
    return observer


def stop_watching(observer: Observer):
    """
    停止文件系统监视器
    
    Args:
        observer: 监视器实例
    """
    if observer and observer.is_alive():
        observer.stop()
        observer.join()
        print("文件系统监视器已停止")
