# -*- coding: utf-8 -*-
"""
导出对话框 - 文件导出功能
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QFileDialog, QMessageBox, QProgressBar, QTextEdit,
                            QGroupBox, QCheckBox, QLineEdit, QRadioButton, QButtonGroup)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager
from models.filesystem_manager import FileSystemManager
from pathlib import Path
import os
import shutil


class ExportWorker(QThread):
    """导出工作线程"""
    
    progress_updated = pyqtSignal(int)  # 进度更新信号
    file_exported = pyqtSignal(str, bool)  # 文件导出完成信号：文件名，是否成功
    export_completed = pyqtSignal(int, int)  # 导出完成信号：成功数量，总数量
    
    def __init__(self, files: list, export_path: str, export_type: str, 
                 jianying_exporter=None):
        super().__init__()
        self.files = files
        self.export_path = export_path
        self.export_type = export_type  # "folder" 或 "jianying"
        self.jianying_exporter = jianying_exporter
        
    def run(self):
        """执行导出任务"""
        success_count = 0
        total_count = len(self.files)
        
        # 导出到文件夹
        export_dir = Path(self.export_path)
        export_dir.mkdir(parents=True, exist_ok=True)

        for i, file_info in enumerate(self.files):
                try:
                    source_path = Path(file_info['path'])
                    if source_path.exists():
                        target_path = export_dir / source_path.name
                        
                        # 如果目标文件已存在，生成新名称
                        counter = 1
                        while target_path.exists():
                            name_part = source_path.stem
                            ext_part = source_path.suffix
                            target_path = export_dir / f"{name_part}_{counter}{ext_part}"
                            counter += 1
                        
                        # 复制文件
                        shutil.copy2(source_path, target_path)
                        success_count += 1
                        self.file_exported.emit(file_info['name'], True)
                    else:
                        self.file_exported.emit(file_info['name'], False)
                        
                except Exception as e:
                    print(f"导出文件失败: {file_info['name']}, 错误: {e}")
                    self.file_exported.emit(file_info['name'], False)
                
                # 更新进度
                progress = int((i + 1) / total_count * 100)
                self.progress_updated.emit(progress)
        
        # 发送导出完成信号
        self.export_completed.emit(success_count, total_count)


class ExportDialog(QDialog):
    """导出对话框"""
    
    def __init__(self, config_manager: ConfigManager, fs_manager: FileSystemManager,
                 selected_files: list, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.fs_manager = fs_manager
        self.selected_files = selected_files
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导出文件")
        self.setFixedSize(500, 600)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导出选中的文件")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件列表
        file_group = QGroupBox(f"要导出的文件 ({len(self.selected_files)} 个)")
        file_layout = QVBoxLayout(file_group)
        
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        
        for file_info in self.selected_files:
            item = QListWidgetItem(file_info['name'])
            item.setToolTip(file_info.get('path', ''))
            self.file_list.addItem(item)
        
        file_layout.addWidget(self.file_list)
        layout.addWidget(file_group)
        
        # 导出类型选择
        type_group = QGroupBox("导出类型")
        type_layout = QVBoxLayout(type_group)
        
        self.export_type_group = QButtonGroup()
        
        self.folder_radio = QRadioButton("导出到文件夹")
        self.folder_radio.setChecked(True)
        self.export_type_group.addButton(self.folder_radio, 0)
        type_layout.addWidget(self.folder_radio)
        

        
        layout.addWidget(type_group)
        
        # 导出路径设置
        path_group = QGroupBox("导出路径")
        path_layout = QVBoxLayout(path_group)
        
        # 文件夹导出路径
        self.folder_path_layout = QHBoxLayout()
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setPlaceholderText("选择导出文件夹...")
        self.folder_browse_btn = QPushButton("浏览")
        self.folder_path_layout.addWidget(QLabel("文件夹:"))
        self.folder_path_layout.addWidget(self.folder_path_edit)
        self.folder_path_layout.addWidget(self.folder_browse_btn)
        path_layout.addLayout(self.folder_path_layout)
        

        
        layout.addWidget(path_group)

        # 导出选项
        options_group = QGroupBox("导出选项")
        options_layout = QVBoxLayout(options_group)

        self.keep_structure_checkbox = QCheckBox("保持文件夹结构")
        self.keep_structure_checkbox.setChecked(True)
        options_layout.addWidget(self.keep_structure_checkbox)

        self.overwrite_checkbox = QCheckBox("覆盖同名文件")
        options_layout.addWidget(self.overwrite_checkbox)

        layout.addWidget(options_group)


        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.export_btn = QPushButton("开始导出")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def setup_connections(self):
        """设置信号连接"""
        self.folder_browse_btn.clicked.connect(self.browse_folder)
        self.export_btn.clicked.connect(self.start_export)
        self.cancel_btn.clicked.connect(self.reject)
    


    
    def browse_folder(self):
        """浏览文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择导出文件夹")
        if folder_path:
            self.folder_path_edit.setText(folder_path)
    

    
    def start_export(self):
        """开始导出"""
        if not self.selected_files:
            QMessageBox.warning(self, "警告", "没有选中的文件")
            return
        
        # 获取导出路径
        export_path = self.folder_path_edit.text().strip()
        if not export_path:
            QMessageBox.warning(self, "警告", "请选择导出文件夹")
            return
        export_type = "folder"
        
        # 显示进度条和日志
        self.progress_bar.setVisible(True)
        self.log_text.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 禁用按钮
        self.export_btn.setEnabled(False)
        
        # 开始导出
        self.export_worker = ExportWorker(
            self.selected_files, export_path, export_type, None
        )
        self.export_worker.progress_updated.connect(self.progress_bar.setValue)
        self.export_worker.file_exported.connect(self.on_file_exported)
        self.export_worker.export_completed.connect(self.on_export_completed)
        self.export_worker.start()
    
    def on_file_exported(self, filename: str, success: bool):
        """单个文件导出完成"""
        status = "成功" if success else "失败"
        self.log_text.append(f"{filename}: {status}")
    
    def on_export_completed(self, success_count: int, total_count: int):
        """导出完成"""
        self.progress_bar.setValue(100)
        
        # 显示结果
        if success_count == total_count:
            QMessageBox.information(
                self, "导出完成", 
                f"成功导出 {success_count} 个文件"
            )
        else:
            QMessageBox.warning(
                self, "导出完成", 
                f"导出完成：成功 {success_count} 个，失败 {total_count - success_count} 个"
            )
        
        # 重新启用按钮
        self.export_btn.setEnabled(True)
        
        # 可以选择关闭对话框
        self.accept()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        
        QComboBox, QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QProgressBar {
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QRadioButton {
            color: #ffffff;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        """
        self.setStyleSheet(style)
