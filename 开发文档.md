# 简笔画素材管理软件开发文档

## 1. 项目概述

### 1.1 项目背景
简笔画素材管理软件是一款专为设计师、剪辑师、创作者打造的素材管理工具。它提供了比普通文件夹更强大的功能，支持可视化管理图片、视频、音频等各类素材文件，并支持拖拽操作，方便用户在不同软件间快速调用素材。

### 1.2 项目目标
- 提供直观、美观的素材管理界面
- 支持多种类型素材的分类管理
- 提供比传统文件管理器更丰富的功能
- 优化素材查找和使用体验
- 提升创作者工作效率

### 1.3 技术栈
- 编程语言：Python
- GUI框架：PyQt6
- 目标平台：Windows、macOS

## 2. 功能规格

### 2.1 核心功能

#### 2.1.1 分类管理
- 五大主分类：人物、场景、道具、其他、回收站
- 每个分类下可创建子文件夹
- 类型限制：
  - 人物、场景、道具分类仅允许存放图片
  - 其他分类可存放音频、视频、文本等多种类型文件
  - 回收站存放删除的文件

#### 2.1.2 文件操作
- 导入：支持批量导入，自动分类
- 导出：支持批量导出至指定位置或剪印草稿
- 拖拽：支持文件自由拖动、跨分类拖动
- 重命名：支持单个或批量重命名
- 删除：支持单个或批量删除至回收站
- 还原：支持从回收站还原文件至原位置

#### 2.1.3 文件预览
- 缩略图显示：所有文件和文件夹均显示预览图
- 双击放大：支持双击查看详情，再次点击退出
- 侧边预览：右侧预览框可展开收起

#### 2.1.4 搜索功能
- 全局搜索或限定分类搜索
- 支持按名称、类型模糊搜索
- 实时显示搜索结果

#### 2.1.5 自定义外观
- 分类标签颜色自定义
- 文件夹、文件名称颜色自定义
- 文件夹缩略图自定义

#### 2.1.6 小窗模式
- 置顶显示小窗口
- 仅显示素材，便于拖拽至其他软件
- 可自由拖动窗口位置

#### 2.1.7 导航功能
- 支持返回上一级和下一级按钮
- 记录浏览历史，便于快速返回之前浏览过的位置
- 视觉设计类似浏览器的前进后退按钮
- 支持鼠标侧键快速导航

### 2.2 快捷键

| 快捷键 | 功能 |
| ------ | ---- |
| F2 | 重命名 |
| Ctrl+F | 搜索栏 |
| 鼠标侧键 | 前进/后退导航 |
| Ctrl+Z | 撤回 |
| Ctrl+A | 全选 |
| Ctrl+1-5键 | 快速切换分类栏 |
| Alt+空格+x | 切换小窗模式 |
| Backspace | 返回上一级 |
| Alt+左箭头 | 返回上一级 |
| Alt+右箭头 | 前进到下一级 |

## 3. 界面设计

### 3.1 总体设计风格
- 整体暗色调界面，白色文字/彩色文字
- 扁平化设计风格
- 轻微动画效果
- 响应式布局

### 3.2 界面布局

#### 3.2.1 主界面
```
+---------------------------------------------------------------+
|                      顶部工具栏                                |
| [←][→][导入] [搜索栏            ] [导出] [排序] [设置] [小窗模式] |
+----------+----------------------------------------------------+
|          |                                        |           |
|          |                                        |           |
|  分类列表  |              文件显示区域               |  预览区域  |
|          |                                        |           |
|          |                                        |           |
|          |                                        |           |
+----------+----------------------------------------------------+
|              底部工具栏                                        |
| [全选] [批量重命名] [删除选中] [更改颜色]                       |
+---------------------------------------------------------------+
```

#### 3.2.2 小窗模式界面
```
+-------------------+
| [←][→][搜索栏  ][X]|
|                   |
|  素材预览区(两列)   |
|                   |
|                   |
+-------------------+
```

### 3.3 颜色方案
- 默认分类颜色：
  - 人物：绿色
  - 场景：红色
  - 道具：黄色
  - 其他：蓝色
  - 回收站：粉色
- 支持16种颜色自定义（不包括黑色和近似黑色）

## 4. 具体功能实现

### 4.1 分类管理实现

#### 4.1.1 人物分类
- 默认子文件夹：主角、路人、怪兽、其他
- 仅允许存放图片格式文件
- 批量重命名支持：名称-表情-动作-后缀格式

#### 4.1.2 场景分类
- 默认子文件夹：室内、室外
- 仅允许存放图片格式文件

#### 4.1.3 道具分类
- 默认子文件夹：武器、物品、载具
- 仅允许存放图片格式文件

#### 4.1.4 其他分类
- 默认子文件夹：BGM、音效、视频、文本、其他
- 允许存放各种格式文件

#### 4.1.5 回收站
- 不允许新建文件和文件夹
- 文件保留30天后自动永久删除
- 支持批量还原和永久删除

### 4.2 文件操作实现

#### 4.2.1 导入功能
- 实现步骤：
  1. 选择目标分类
  2. 选择要导入的文件
  3. 检查文件类型是否符合分类要求
  4. 检查是否有重复文件
  5. 如有重复，显示预览和重命名窗口
  6. 导入文件并生成缩略图

#### 4.2.2 导出功能
- 实现步骤：
  1. 选择要导出的文件（通过复选框）
  2. 点击导出按钮
  3. 自动检测剪映草稿路径：
     - Windows系统：检测`%USERPROFILE%\AppData\Local\JianyingPro\User Data\Projects\com.lveditor.draft`
     - macOS系统：检测`~/Movies/JianyingPro/User Data/Projects/com.lveditor.draft`
  4. 读取草稿文件夹中的工程文件列表，显示工程名称和创建时间
  5. 用户选择特定的剪映工程或普通文件夹作为导出位置
  6. 执行导出操作，将选中文件复制到目标位置

#### 4.2.3 批量重命名
- 人物分类重命名格式：名称-表情-动作-后缀
  - 例如：男1-害羞
  - 名称为必填，其他可选
  - 连接符自动添加
- 其他分类重命名：
  - 支持按时间顺序重命名
  - 支持自定义后缀

#### 4.2.4 拖放操作
- 支持内部拖放：在软件内部调整文件位置
- 支持外部拖放：将文件拖出至其他应用
- 分类限制：根据目标分类的规则限制可拖入的文件类型

### 4.3 用户界面功能

#### 4.3.1 搜索功能
- 全局搜索：在所有分类中搜索
- 分类搜索：限定在特定分类中搜索
- 支持按名称、类型搜索
- 实时显示搜索结果

#### 4.3.2 排序功能
- 按创建时间排序
- 按文件后缀排序
- 按文件类型排序

#### 4.3.3 预览功能
- 缩略图生成：
  - 图片：直接缩放
  - 视频：提取关键帧
  - 音频：显示波形图标
  - 文本：显示文档图标
- 右侧预览区：显示选中文件的详情
- 双击放大：全屏预览模式

#### 4.3.4 右键菜单
- 通用菜单项：复制、粘贴、重命名、导入、导出、搜索、删除选中、更改颜色等
- 回收站特殊菜单：批量还原、筛选、搜索、更改排序方式、批量删除

#### 4.3.5 小窗模式
- 触发方式：点击小窗模式按钮或使用Alt+空格+x快捷键
- 窗口位置：默认右下角，可自由拖动
- 窗口特性：置顶显示、双列显示素材
- 功能限制：仅保留搜索和退出功能

#### 4.3.6 导航功能
- 上一级/下一级按钮实现：
  1. 在顶部工具栏左侧添加返回和前进按钮
  2. 记录用户浏览历史到导航栈中
  3. 返回按钮点击时回到上一个浏览位置
  4. 前进按钮点击时前往下一个浏览位置
  5. 当无历史记录时按钮呈禁用状态
- 支持的操作：
  - 点击按钮导航
  - 使用鼠标侧键导航
  - 使用键盘快捷键导航
- 导航历史记录：
  - 存储最近访问的30个位置
  - 当打开新位置时记录到历史
  - 切换目录、分类或搜索结果时均记录历史

## 5. 数据结构设计

### 5.1 文件存储结构
```
    /config/
        settings.json
        colors.json
        metadata.db
        navigation_history.json
```

### 5.2 配置文件结构
- settings.json: 全局设置
  ```json
  {
    "storage_path": "路径字符串",
    "jianying_draft_path": "自动检测的剪映草稿路径",
    "jianying_projects": [
      {"name": "项目1", "path": "项目1路径", "last_modified": "2023-07-15T14:30:45"},
      {"name": "项目2", "path": "项目2路径", "last_modified": "2023-07-16T09:15:22"}
    ],
    "thumbnail_size": 100,
    "first_run": false,
    "window_position": {"x": 0, "y": 0, "width": 800, "height": 600},
    "mini_window": {"x": 0, "y": 0, "width": 300, "height": 400},
    "navigation_history_limit": 30
  }
  ```

- colors.json: 颜色配置
  ```json
  {
    "categories": {
      "人物": "#00FF00",
      "场景": "#FF0000",
      "道具": "#FFFF00",
      "其他": "#0000FF",
      "回收站": "#FF00FF"
    },
    "custom_colors": {
      "folder_1": "#FF9900",
      "file_1": "#CC33FF"
    }
  }
  ```

- navigation_history.json: 导航历史记录
  ```json
  {
    "history": [
      {"path": "/人物/主角", "timestamp": "2023-07-15T14:30:45"},
      {"path": "/场景/室内", "timestamp": "2023-07-15T14:32:12"},
      {"path": "/其他/BGM", "timestamp": "2023-07-15T14:35:20"}
    ],
    "current_index": 2
  }
  ```

### 5.3 元数据数据库结构
使用SQLite数据库存储文件元数据信息

- 文件表(Files)
  ```sql
  CREATE TABLE Files (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    category TEXT NOT NULL,
    type TEXT NOT NULL,
    creation_time DATETIME,
    modified_time DATETIME,
    custom_color TEXT,
    original_path TEXT,  -- 用于回收站还原
    delete_time DATETIME  -- 用于回收站自动清理
  );
  ```

- 文件夹表(Folders)
  ```sql
  CREATE TABLE Folders (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    category TEXT NOT NULL,
    custom_color TEXT,
    custom_thumbnail TEXT
  );
  ```

## 6. 开发计划

本计划采用迭代开发模式，将项目分为四个核心里程碑。每个里程碑都旨在交付一个功能递增、可测试的软件版本，以便在开发过程中持续获得反馈并进行调整。

### 6.1 里程碑一：基础框架与核心文件系统（预计2周）
**目标**：搭建项目的基础结构，实现文件的存储和分类管理，让软件达到基本可用的状态。
**可交付成果**：
- 一个可以启动并显示主窗口的应用程序。
- 左侧分类栏可以显示固定的五大分类，并能响应点击。
- 文件显示区域可以展示指定物理文件夹内的文件和子文件夹。
- 实现文件的导入功能（即将文件复制到程序管理的目录中）。
- 数据库表结构创建完毕，能够存储文件元数据。

**任务分解**：
- [ ] **1.1 环境搭建与项目初始化**
  - [ ] 配置Python虚拟环境并安装PyQt6等核心依赖。
  - [ ] 创建`src`目录结构和`main.py`程序入口。
  - [ ] 初始化Git仓库并编写`.gitignore`文件。
- [ ] **1.2 主窗口UI框架**
  - [ ] 使用代码或Qt Designer创建主窗口的核心布局（`main_window.py`），包括顶部工具栏、分类列表、文件视图、预览区和底部工具栏的占位。
- [ ] **1.3 分类系统（静态）**
  - [ ] 在`ui/category_list.py`中实现左侧分类列表的UI。
  - [ ] 静态展示“人物”、“场景”、“道具”、“其他”、“回收站”五个分类。
  - [ ] 实现点击不同分类时，文件视图能够切换到对应的根目录。
- [ ] **1.4 文件存储与数据库**
  - [ ] 在`models/database.py`中设计并创建SQLite数据库表结构。
  - [ ] 在`utils/file_operations.py`中编写基础的文件操作函数（如导入文件时复制文件、记录到数据库等）。
- [ ] **1.5 文件视图**
  - [ ] 在`ui/file_view.py`中实现文件和文件夹的网格/列表视图。
  - [ ] 实现从数据库读取元数据，并在视图中展示。
  - [ ] 实现双击进入子文件夹的功能。

### 6.2 里程碑二：核心交互功能完善（预计3周）
**目标**：聚焦于提升用户交互体验，实现文件的主要操作功能。
**可交付成果**：
- 功能完整的拖放操作（应用内外）。
- 可用的实时搜索和排序功能。
- 文件和文件夹的重命名、删除、全选等基础操作。
- 右侧预览面板可以显示选中文件的基本信息和缩略图。

**任务分解**：
- [ ] **2.1 文件拖放功能**
  - [ ] 实现应用内文件/文件夹的拖放，以移动到不同分类或子文件夹。
  - [ ] 实现从操作系统拖入文件到窗口，触发导入逻辑。
  - [ ] 实现从应用内将文件拖出到其他软件（如桌面、编辑器）。
- [ ] **2.2 文件预览功能**
  - [ ] 在`ui/preview_panel.py`中构建右侧预览面板UI。
  - [ ] 在`utils/thumbnail.py`中实现图片文件的缩略图生成与缓存。
  - [ ] 采用异步方式生成视频、音频文件的图标或关键帧，避免UI卡顿。
  - [ ] 实现双击文件弹出放大预览窗口的功能。
- [ ] **2.3 搜索与排序**
  - [ ] 在顶部工具栏实现搜索框UI和逻辑。
  - [ ] 实现基于文件名、类型的实时模糊搜索。
  - [ ] 在工具栏添加排序按钮，实现按名称、类型、修改时间排序。
- [ ] **2.4 文件基础操作**
  - [ ] 实现通过`F2`或右键菜单重命名文件/文件夹的功能。
  - [ ] 实现删除文件至回收站（逻辑删除，更新数据库状态）。
  - [ ] 完善底部工具栏功能，如全选、批量删除。
  - [ ] 为文件/文件夹添加右键上下文菜单。

### 6.3 里程碑三：高级功能与个性化（预计3周）
**目标**：开发软件的特色高级功能，提升专业性和易用性。
**可交付成果**：
- 功能完整的小窗模式。
- 完善的回收站管理功能。
- 支持前进/后退的导航历史功能。
- 导出到剪映草稿的特色功能。
- 自定义主题颜色。

**任务分解**：
- [ ] **3.1 小窗模式 (`ui/mini_window.py`)**
  - [ ] 实现小窗模式的UI布局。
  - [ ] 实现窗口置顶显示、自由拖动。
  - [ ] 实现通过按钮和快捷键`Alt+空格+x`在主窗口与小窗模式间切换。
  - [ ] 小窗模式中保留核心的搜索和拖出功能。
- [ ] **3.2 回收站管理 (`ui/recycle_bin.py`)**
  - [ ] 创建回收站的专属管理界面。
  - [ ] 实现文件的“还原”和“永久删除”功能。
  - [ ] 开发一个后台任务，用于自动清理超过30天的文件。
- [ ] **3.3 导航历史功能**
  - [ ] 在顶部工具栏实现前进/后退按钮，并根据历史记录状态切换可用/禁用。
  - [ ] 使用列表或双向链表数据结构来记录导航历史。
  - [ ] 绑定鼠标侧键和键盘快捷键 (`Alt+左/右箭头`) 以触发导航。
  - [ ] 将导航历史通过JSON格式持久化存储。
- [ ] **3.4 导出到剪映**
  - [ ] 实现自动检测Windows和macOS下的剪映草稿路径。
  - [ ] 实现读取并向用户展示剪映工程列表。
  - [ ] 实现将选中的素材文件复制到指定工程目录的功能。
- [ ] **3.5 自定义外观**
  - [ ] 实现分类标签、文件夹和文件名的颜色自定义。
  - [ ] 创建一个颜色选择器组件 (`ui/color_picker.py`)。
  - [ ] 将颜色配置持久化到`colors.json`文件中。

### 6.4 里程碑四：优化、测试与打包（预计2周）
**目标**：全面提升软件质量，修复潜在BUG，进行性能和体验优化，并准备最终发布。
**可交付成果**：
- 一个稳定、流畅、界面美观的应用程序。
- 良好的跨平台（Windows, macOS）兼容性。
- 可供用户直接下载安装的软件包。

**任务分解**：
- [ ] **4.1 性能优化**
  - [ ] 使用`cProfile`等工具分析和优化性能瓶颈，如启动速度、文件加载速度。
  - [ ] 优化缩略图加载机制，引入懒加载和二级缓存。
  - [ ] 确保处理大量文件时UI响应流畅，必要时使用多线程。
- [ ] **4.2 UI/UX优化**
  - [ ] 在`utils/animations.py`中添加平滑的过渡动画效果。
  - [ ] 在`ui/styles.py`中统一和美化QSS样式表。
  - [ ] 优化应用图标和字体，提升整体视觉体验。
- [ ] **4.3 全面测试**
  - [ ] 在`tests/`目录下编写关键功能的单元测试。
  - [ ] 在Windows和macOS环境下进行兼容性测试。
  - [ ] 模拟真实用户场景，进行端到端的流程测试。
- [ ] **4.4 打包与发布**
  - [ ] 使用PyInstaller或同类工具为不同平台创建可执行文件。
  - [ ] 更新`README.md`和编写详细的用户使用说明。
  - [ ] 准备发布v1.0.0版本。

## 7. 注意事项和限制

### 7.1 性能考量
- 大量文件处理时避免UI卡顿
- 缩略图生成采用异步方式
- 大文件预览考虑内存使用

### 7.2 平台差异
- Windows与Mac系统文件路径差异处理
- 系统快捷键差异适配
- 界面渲染差异优化

### 7.3 可能的扩展
- 云同步功能
- 标签系统
- 插件系统
- 批量处理工具

## 8. 技术依赖

### 8.1 核心依赖
- Python 3.11.8
PyQt6>=6.8.1
Pillow>=10.4.0
- FFmpeg (视频处理)
- SQLite (数据存储)
- json (解析剪映工程文件)
- os, platform (检测系统路径)

### 8.2 开发工具
- 集成开发环境：PyCharm或VS Code
- 版本控制：Git
- 打包工具：PyInstaller
- 界面设计工具：Qt Designer

## 9. 测试计划

### 9.1 单元测试
- 文件操作功能测试
- 数据库操作测试
- 界面组件测试

### 9.2 集成测试
- 各模块联动测试
- 拖放功能测试
- 批量操作测试

### 9.3 用户测试
- 界面易用性测试
- 常用场景测试
- 性能和稳定性测试

## 10. 总结

简笔画素材管理软件是一个专为创意工作者设计的素材管理工具，提供比传统文件管理器更强大的功能和更好的用户体验。通过分类管理、预览功能、拖放操作和小窗模式，用户可以更高效地管理和使用各种素材文件。软件采用PyQt6开发，支持Windows和macOS平台，具有现代化、美观的界面设计。 