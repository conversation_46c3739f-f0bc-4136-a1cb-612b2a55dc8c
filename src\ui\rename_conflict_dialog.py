# -*- coding: utf-8 -*-
"""
重命名冲突对话框 - 处理文件名冲突时的重命名
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QGroupBox, QGridLayout)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap
from pathlib import Path
import os


class RenameConflictDialog(QDialog):
    """重命名冲突对话框"""
    
    # 信号
    rename_confirmed = pyqtSignal(str)  # 确认重命名信号，传递新文件名
    
    def __init__(self, original_name: str, conflicting_path: str, preview_path: str = None, parent=None):
        super().__init__(parent)
        self.original_name = original_name
        self.conflicting_path = conflicting_path
        self.preview_path = preview_path
        self.new_name = ""
        
        self.init_ui()
        self.setup_connections()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("文件名冲突")
        self.setModal(True)
        self.setFixedSize(600, 400)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("检测到文件名冲突")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 冲突信息
        info_label = QLabel(f"文件名 '{self.original_name}' 已存在，请选择处理方式：")
        info_label.setFont(QFont("Microsoft YaHei", 10))
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 预览区域
        preview_group = QGroupBox("文件预览")
        preview_layout = QGridLayout(preview_group)
        
        # 现有文件预览
        existing_label = QLabel("现有文件:")
        existing_label.setFont(QFont("Microsoft YaHei", 9, QFont.Weight.Bold))
        preview_layout.addWidget(existing_label, 0, 0)
        
        self.existing_preview = QLabel()
        self.existing_preview.setFixedSize(150, 150)
        self.existing_preview.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                background-color: #f5f5f5;
            }
        """)
        self.existing_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.load_preview(self.conflicting_path, self.existing_preview)
        preview_layout.addWidget(self.existing_preview, 1, 0)
        
        existing_info = QLabel(f"文件名: {Path(self.conflicting_path).name}")
        existing_info.setWordWrap(True)
        preview_layout.addWidget(existing_info, 2, 0)
        
        # 新文件预览
        if self.preview_path:
            new_label = QLabel("新文件:")
            new_label.setFont(QFont("Microsoft YaHei", 9, QFont.Weight.Bold))
            preview_layout.addWidget(new_label, 0, 1)
            
            self.new_preview = QLabel()
            self.new_preview.setFixedSize(150, 150)
            self.new_preview.setStyleSheet("""
                QLabel {
                    border: 1px solid #ccc;
                    background-color: #f5f5f5;
                }
            """)
            self.new_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.load_preview(self.preview_path, self.new_preview)
            preview_layout.addWidget(self.new_preview, 1, 1)
            
            new_info = QLabel(f"文件名: {self.original_name}")
            new_info.setWordWrap(True)
            preview_layout.addWidget(new_info, 2, 1)
        
        layout.addWidget(preview_group)
        
        # 重命名输入
        rename_group = QGroupBox("重命名")
        rename_layout = QVBoxLayout(rename_group)
        
        rename_info = QLabel("请输入新的文件名（不包含扩展名）：")
        rename_layout.addWidget(rename_info)
        
        input_layout = QHBoxLayout()
        
        # 提取文件名和扩展名
        name_parts = os.path.splitext(self.original_name)
        self.base_name = name_parts[0]
        self.extension = name_parts[1]
        
        self.name_edit = QLineEdit()
        self.name_edit.setText(self.base_name)
        self.name_edit.selectAll()
        input_layout.addWidget(self.name_edit)
        
        ext_label = QLabel(self.extension)
        ext_label.setFont(QFont("Microsoft YaHei", 10))
        input_layout.addWidget(ext_label)
        
        rename_layout.addLayout(input_layout)
        
        # 自动生成建议
        suggestion_layout = QHBoxLayout()
        suggestion_layout.addWidget(QLabel("建议名称："))
        
        self.suggestion_btn1 = QPushButton(f"{self.base_name}_副本")
        self.suggestion_btn1.clicked.connect(lambda: self.name_edit.setText(f"{self.base_name}_副本"))
        suggestion_layout.addWidget(self.suggestion_btn1)
        
        self.suggestion_btn2 = QPushButton(f"{self.base_name}_新")
        self.suggestion_btn2.clicked.connect(lambda: self.name_edit.setText(f"{self.base_name}_新"))
        suggestion_layout.addWidget(self.suggestion_btn2)
        
        suggestion_layout.addStretch()
        rename_layout.addLayout(suggestion_layout)
        
        layout.addWidget(rename_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        button_layout.addStretch()
        
        self.overwrite_btn = QPushButton("覆盖现有文件")
        self.overwrite_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
        """)
        self.overwrite_btn.clicked.connect(self.overwrite_file)
        button_layout.addWidget(self.overwrite_btn)
        
        self.rename_btn = QPushButton("重命名")
        self.rename_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.rename_btn.clicked.connect(self.confirm_rename)
        self.rename_btn.setDefault(True)
        button_layout.addWidget(self.rename_btn)
        
        layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.name_edit.textChanged.connect(self.validate_name)
        
    def load_preview(self, file_path: str, label: QLabel):
        """加载文件预览"""
        try:
            if not file_path or not os.path.exists(file_path):
                label.setText("无预览")
                return
                
            # 检查是否是图片文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext in image_extensions:
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # 缩放图片以适应预览区域
                    scaled_pixmap = pixmap.scaled(
                        140, 140, 
                        Qt.AspectRatioMode.KeepAspectRatio, 
                        Qt.TransformationMode.SmoothTransformation
                    )
                    label.setPixmap(scaled_pixmap)
                else:
                    label.setText("图片加载失败")
            else:
                # 非图片文件显示文件类型
                label.setText(f"文件类型:\n{file_ext.upper()}")
                
        except Exception as e:
            label.setText("预览失败")
            print(f"预览加载失败: {e}")
    
    def validate_name(self):
        """验证文件名"""
        name = self.name_edit.text().strip()
        
        # 检查文件名是否有效
        invalid_chars = '<>:"/\\|?*'
        has_invalid = any(char in name for char in invalid_chars)
        
        if not name or has_invalid:
            self.rename_btn.setEnabled(False)
        else:
            self.rename_btn.setEnabled(True)
    
    def confirm_rename(self):
        """确认重命名"""
        new_base_name = self.name_edit.text().strip()
        if new_base_name:
            self.new_name = new_base_name + self.extension
            self.rename_confirmed.emit(self.new_name)
            self.accept()
    
    def overwrite_file(self):
        """覆盖现有文件"""
        self.new_name = self.original_name  # 保持原名
        self.rename_confirmed.emit(self.new_name)
        self.accept()
    
    def get_new_name(self) -> str:
        """获取新文件名"""
        return self.new_name
